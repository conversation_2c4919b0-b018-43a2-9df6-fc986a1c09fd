-- Update RLS policies to require authentication for admin operations
-- This migration ensures that only authenticated users can access admin functions

-- Drop existing policies
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON brands;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON pages;
DROP POLICY IF EXISTS "Enable all access for authenticated users" ON settings;

-- Create new policies that properly check for authenticated users
CREATE POLICY "Enable all access for authenticated users" ON brands 
FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Enable all access for authenticated users" ON products 
FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Enable all access for authenticated users" ON pages 
FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Enable all access for authenticated users" ON settings 
FOR ALL USING (auth.uid() IS NOT NULL);

-- Ensure public read access is still available for the frontend
-- These policies allow unauthenticated users to read published content
CREATE POLICY "Enable read access for all users on brands" ON brands 
FOR SELECT USING (true);

CREATE POLICY "Enable read access for all users on products" ON products 
FOR SELECT USING (true);

CREATE POLICY "Enable read access for published pages" ON pages 
FOR SELECT USING (published = true);

CREATE POLICY "Enable read access for all users on settings" ON settings 
FOR SELECT USING (true);
