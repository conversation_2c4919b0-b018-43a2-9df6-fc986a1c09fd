/**
 * Modern Product Specifications Component
 * Displays product specifications in a categorized, searchable table
 */

import React, { useState, useMemo } from 'react';
import { Search, Filter, Download, Copy, Check } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ProductWithBrand } from '@/services/productService';

interface ModernProductSpecificationsProps {
  product: ProductWithBrand;
  className?: string;
}

const ModernProductSpecifications = ({ product, className = "" }: ModernProductSpecificationsProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [copiedSpec, setCopiedSpec] = useState<string | null>(null);

  // Convert specifications object to array format
  const allSpecs = useMemo(() => {
    if (!product.specifications) return [];

    return Object.entries(product.specifications).map(([key, value], index) => ({
      id: key,
      name: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
      value: String(value),
      unit: '',
      category: getCategoryFromKey(key),
    }));
  }, [product.specifications]);

  // Helper function to categorize specifications
  const getCategoryFromKey = (key: string): string => {
    const lowerKey = key.toLowerCase();
    if (lowerKey.includes('power') || lowerKey.includes('voltage') || lowerKey.includes('current')) {
      return 'electrical';
    }
    if (lowerKey.includes('capacity') || lowerKey.includes('flow') || lowerKey.includes('pressure')) {
      return 'performance';
    }
    if (lowerKey.includes('dimension') || lowerKey.includes('weight') || lowerKey.includes('size')) {
      return 'physical';
    }
    if (lowerKey.includes('temp') || lowerKey.includes('humidity') || lowerKey.includes('efficiency')) {
      return 'environmental';
    }
    return 'general';
  };

  // Get unique categories
  const categories = useMemo(() => {
    const cats = [...new Set(allSpecs.map(spec => spec.category))];
    return cats.sort();
  }, [allSpecs]);

  // Filter specifications
  const filteredSpecs = useMemo(() => {
    return allSpecs.filter(spec => {
      const matchesSearch = searchTerm === '' ||
        spec.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        spec.value.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = selectedCategory === 'all' || spec.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [allSpecs, searchTerm, selectedCategory]);

  // Group specifications by category
  const groupedSpecs = useMemo(() => {
    const groups: Record<string, typeof filteredSpecs> = {};
    filteredSpecs.forEach(spec => {
      if (!groups[spec.category]) {
        groups[spec.category] = [];
      }
      groups[spec.category].push(spec);
    });
    return groups;
  }, [filteredSpecs]);

  const copySpecification = async (spec: any) => {
    const text = `${spec.name}: ${spec.value} ${spec.unit}`.trim();
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSpec(spec.id);
      setTimeout(() => setCopiedSpec(null), 2000);
    } catch (err) {
      console.error('Failed to copy specification:', err);
    }
  };

  const exportSpecifications = () => {
    const csvContent = [
      ['Specification', 'Value', 'Unit', 'Category'],
      ...filteredSpecs.map(spec => [spec.name, spec.value, spec.unit, spec.category])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${attributes.name}-specifications.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getCategoryDisplayName = (category: string) => {
    const categoryMap: Record<string, string> = {
      'performance': 'Performance',
      'physical': 'Physical Dimensions',
      'electrical': 'Electrical',
      'environmental': 'Environmental',
      'general': 'General',
    };
    return categoryMap[category] || category.charAt(0).toUpperCase() + category.slice(1);
  };

  const getCategoryColor = (category: string) => {
    const colorMap: Record<string, string> = {
      'performance': 'bg-blue-100 text-blue-800',
      'physical': 'bg-green-100 text-green-800',
      'electrical': 'bg-yellow-100 text-yellow-800',
      'environmental': 'bg-purple-100 text-purple-800',
      'general': 'bg-gray-100 text-gray-800',
    };
    return colorMap[category] || 'bg-gray-100 text-gray-800';
  };

  if (allSpecs.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500">No specifications available for this product.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search specifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {getCategoryDisplayName(category)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={exportSpecifications}
          className="flex items-center gap-2"
        >
          <Download className="w-4 h-4" />
          Export CSV
        </Button>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredSpecs.length} of {allSpecs.length} specifications
        </p>

        {Object.keys(groupedSpecs).length > 1 && (
          <div className="flex gap-2">
            {Object.keys(groupedSpecs).map(category => (
              <Badge key={category} className={getCategoryColor(category)}>
                {getCategoryDisplayName(category)} ({groupedSpecs[category].length})
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Specifications Table */}
      {filteredSpecs.length > 0 ? (
        <div className="space-y-6">
          {Object.entries(groupedSpecs).map(([category, specs]) => (
            <div key={category} className="bg-white rounded-lg border overflow-hidden">
              <div className={`px-4 py-3 border-b ${getCategoryColor(category)}`}>
                <h3 className="font-semibold">{getCategoryDisplayName(category)}</h3>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Specification</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Value</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Unit</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {specs.map((spec, index) => (
                      <tr key={spec.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-4 py-3 text-sm font-medium text-gray-900">
                          {spec.name}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-700 font-mono">
                          {spec.value}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {spec.unit}
                        </td>
                        <td className="px-4 py-3 text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copySpecification(spec)}
                            className="h-8 w-8 p-0"
                          >
                            {copiedSpec === spec.id ? (
                              <Check className="w-4 h-4 text-green-600" />
                            ) : (
                              <Copy className="w-4 h-4" />
                            )}
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">No specifications match your search criteria.</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSearchTerm('');
              setSelectedCategory('all');
            }}
            className="mt-2"
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};

export default ModernProductSpecifications;
