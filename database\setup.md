# Database Setup Guide

This guide will help you set up the Supabase database for the Nile Pro MEP CMS.

## Prerequisites

1. Access to your Supabase project dashboard
2. Database URL and API keys (already configured in the project)

## Setup Steps

### 1. Run Database Migrations

1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database/migrations/001_initial_schema.sql`
4. Click "Run" to execute the migration

This will create:
- `brands` table for HVAC brand information
- `products` table for product catalog
- `pages` table for CMS content management
- `settings` table for site configuration
- Proper indexes for performance
- Row Level Security (RLS) policies
- Automatic `updated_at` triggers

### 2. Seed Initial Data

1. In the SQL Editor, copy and paste the contents of `database/seeds/001_initial_data.sql`
2. Click "Run" to populate the database with sample data

This will add:
- Sample HVAC brands (Carrier, Trane, York, Daikin, Mitsubishi Electric, LG)
- Sample products for each brand
- Basic CMS pages (About Us, Contact)
- Default site settings

### 3. Configure Authentication (Required for CMS Access)

The CMS requires authentication for admin access. Follow these steps:

#### 3.1 Enable Email Authentication
1. Go to Authentication > Settings in your Supabase dashboard
2. Under "Auth Providers", ensure "Email" is enabled
3. Configure the following settings:
   - **Enable email confirmations**: Disabled (for easier setup)
   - **Enable email change confirmations**: Disabled
   - **Enable manual linking**: Enabled

#### 3.2 Create Admin User
1. Go to Authentication > Users in your Supabase dashboard
2. Click "Add user"
3. Enter admin credentials:
   - **Email**: <EMAIL>
   - **Password**: Create a secure password
   - **Email Confirm**: Check this box
4. Click "Create user"

#### 3.3 Update RLS Policies (Important)
1. In the SQL Editor, copy and paste the contents of `database/migrations/002_update_auth_policies.sql`
2. Click "Run" to update the authentication policies

This migration will:
- Update RLS policies to require authentication for admin operations
- Maintain public read access for the frontend
- Ensure proper security for the CMS

#### 3.4 Test Authentication
1. After completing the setup, navigate to `/admin` in your application
2. You should be redirected to a login page
3. Use the admin credentials you created to log in

### 4. Set Up Storage (Optional)

For file uploads (product images, documents):

1. Go to Storage in your Supabase dashboard
2. Create a new bucket called `uploads`
3. Set appropriate policies for public read access

## Database Schema Overview

### Brands Table
- Stores HVAC manufacturer information
- Fields: name, logo, description, website, country, established year

### Products Table
- Complete product catalog with specifications
- Linked to brands via foreign key
- Supports JSON specifications, feature arrays, and image arrays
- Includes SEO-friendly slugs and featured product flags

### Pages Table
- CMS content management
- JSON content field for flexible page structures
- SEO meta fields and publish status

### Settings Table
- Site-wide configuration
- Key-value pairs with JSON values for complex settings

## Security

The database uses Row Level Security (RLS) with the following policies:
- Public read access for all content
- Authenticated user access for admin operations
- Published-only access for pages

## Next Steps

After setting up the database:
1. Test the CMS service by running the development server
2. Access the admin interface to manage content
3. Customize the schema as needed for your specific requirements
