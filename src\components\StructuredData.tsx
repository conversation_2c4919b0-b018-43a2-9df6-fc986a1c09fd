import { useEffect } from 'react';
import { Product } from '@/types/product';

interface StructuredDataProps {
  type: 'product' | 'organization' | 'breadcrumb';
  data: any;
}

export const generateProductStructuredData = (product: Product) => {
  const mainImage = product.images.find(img => img.isMain) || product.images[0];

  return {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": product.title,
    "description": product.fullDescription,
    "image": mainImage?.url,
    "brand": {
      "@type": "Brand",
      "name": "Nile Pro MEP"
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "Nile Pro MEP",
      "url": window.location.origin
    },
    "category": product.category.replace('-', ' '),
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceCurrency": "USD",
      "seller": {
        "@type": "Organization",
        "name": "Nile Pro MEP"
      }
    },
    "additionalProperty": product.specifications.map(spec => ({
      "@type": "PropertyValue",
      "name": spec.name,
      "value": `${spec.value} ${spec.unit || ''}`.trim()
    })),
    "applicationCategory": product.applications.map(app => app.sector)
  };
};

export const generateOrganizationStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Nile Pro MEP",
    "description": "Expert MEP contractor delivering comprehensive MEP installation works with advanced technology and durable design.",
    "url": window.location.origin,
    "logo": `${window.location.origin}/logo.png`,
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-123-4567",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "sameAs": [
      "https://www.linkedin.com/company/nile-pro-mep",
      "https://twitter.com/nileproMEP"
    ]
  };
};

export const generateBreadcrumbStructuredData = (items: Array<{ name: string, url: string }>) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
};

const StructuredData = ({ type, data }: StructuredDataProps) => {
  useEffect(() => {
    let structuredData;

    switch (type) {
      case 'product':
        structuredData = generateProductStructuredData(data);
        break;
      case 'organization':
        structuredData = generateOrganizationStructuredData();
        break;
      case 'breadcrumb':
        structuredData = generateBreadcrumbStructuredData(data);
        break;
      default:
        return;
    }

    // Remove existing structured data of the same type
    const existingScript = document.querySelector(`script[data-structured-data="${type}"]`);
    if (existingScript) {
      existingScript.remove();
    }

    // Add new structured data
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-structured-data', type);
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    // Cleanup on unmount
    return () => {
      const scriptToRemove = document.querySelector(`script[data-structured-data="${type}"]`);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [type, data]);

  return null;
};

export default StructuredData;
