import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Send,
  Building,
  Users,
  Award
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

const Contact = () => {
  const contactInfo = [
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Phone",
      details: ["+****************", "+****************"],
      subtitle: "Call us anytime"
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"],
      subtitle: "Send us your inquiry"
    },
    {
      icon: <MapPin className="h-6 w-6" />,
      title: "Location",
      details: ["123 Construction Ave", "Cairo, Egypt 12345"],
      subtitle: "Visit our office"
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Working Hours",
      details: ["Mon - Fri: 8:00 AM - 6:00 PM", "Sat: 9:00 AM - 4:00 PM"],
      subtitle: "We're here to help"
    }
  ];

  const stats = [
    {
      icon: <Building className="h-8 w-8" />,
      number: "50+",
      label: "Projects Completed"
    },
    {
      icon: <Users className="h-8 w-8" />,
      number: "50+",
      label: "Expert Engineers"
    },
    {
      icon: <Award className="h-8 w-8" />,
      number: "9+",
      label: "Years Experience"
    }
  ];

  return (
    <section id="contact" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Get In <span className="text-primary">Touch</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Ready to discuss your MEP project? Contact our experts for a free consultation
            and discover how we can bring your vision to life.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16">
          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card className="border-border shadow-large">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-foreground flex items-center">
                  <Send className="h-6 w-6 text-primary mr-3" />
                  Send Us a Message
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      First Name *
                    </label>
                    <Input placeholder="John" className="border-border" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Last Name *
                    </label>
                    <Input placeholder="Doe" className="border-border" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Email Address *
                    </label>
                    <Input type="email" placeholder="<EMAIL>" className="border-border" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Phone Number
                    </label>
                    <Input placeholder="+****************" className="border-border" />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Project Type
                  </label>
                  <select className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground">
                    <option value="">Select project type</option>
                    <option value="commercial">Commercial Building</option>
                    <option value="industrial">Industrial Facility</option>
                    <option value="residential">Residential Complex</option>
                    <option value="healthcare">Healthcare Facility</option>
                    <option value="educational">Educational Institution</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Message *
                  </label>
                  <Textarea
                    placeholder="Tell us about your project requirements..."
                    className="min-h-[120px] border-border"
                  />
                </div>

                <Button className="w-full bg-gradient-primary hover:opacity-90 shadow-primary text-white py-3">
                  Send Message
                  <Send className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            {contactInfo.map((info, index) => (
              <Card key={index} className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-primary/10 rounded-lg">
                      <div className="text-primary">
                        {info.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-foreground mb-1">{info.title}</h3>
                      <p className="text-sm text-muted-foreground mb-2">{info.subtitle}</p>
                      {info.details.map((detail, detailIndex) => (
                        <p key={detailIndex} className="text-sm text-foreground">{detail}</p>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Stats */}
            <Card className="border-primary/20 bg-gradient-primary text-white">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-6 text-center">Why Choose Nile Pro?</h3>
                <div className="space-y-4">
                  {stats.map((stat, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="p-2 bg-white/20 rounded-lg">
                        {stat.icon}
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{stat.number}</div>
                        <div className="text-sm opacity-90">{stat.label}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Map or Additional CTA */}
        <Card className="border-border">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Get a free consultation and detailed project estimate. Our team of experts
              is ready to help you achieve your MEP construction goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-primary hover:opacity-90 shadow-primary"
                onClick={() => {
                  // Navigate to contact page
                  window.location.href = '/contact';
                }}
              >
                Schedule Consultation
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-primary text-primary hover:bg-primary hover:text-white"
                onClick={() => {
                  // Download portfolio PDF
                  const link = document.createElement('a');
                  link.href = '/downloads/nile-pro-portfolio.pdf';
                  link.download = 'Nile-Pro-MEP-Portfolio.pdf';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                Download Portfolio
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default Contact;