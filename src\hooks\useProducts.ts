/**
 * Custom React hooks for product data fetching
 * Uses React Query for caching and state management
 */

import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { productService, ProductQueryParams, ProductWithBrand } from '../services/productService';

// Query keys for consistent caching
export const QUERY_KEYS = {
  products: 'products',
  product: 'product',
  featuredProducts: 'featuredProducts',
  popularProducts: 'popularProducts',
  relatedProducts: 'relatedProducts',
  categories: 'categories',
  brands: 'brands',
} as const;

/**
 * Hook for fetching products with filtering and pagination
 */
export const useProducts = (params: ProductQueryParams = {}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.products, params],
    queryFn: async () => {
      console.log('useProducts hook - Starting query with params:', params) // Debug log
      try {
        const result = await productService.getProducts(params)
        console.log('useProducts hook - Query successful:', result) // Debug log
        return result
      } catch (error) {
        console.error('useProducts hook - Query failed:', error) // Debug log
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    keepPreviousData: true, // Smooth pagination
    retry: (failureCount, error) => {
      console.log('useProducts hook - Retry attempt:', failureCount, error) // Debug log
      return failureCount < 3
    },
    onError: (error) => {
      console.error('useProducts hook - Final error:', error) // Debug log
    }
  });
};

/**
 * Hook for fetching a single product by slug
 */
export const useProduct = (slug: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.product, slug],
    queryFn: () => productService.getProductBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry for 404 errors
      if (error?.response?.status === 404) return false;
      return failureCount < 3;
    },
  });
};

/**
 * Hook for fetching a single product by slug (alias for useProduct)
 */
export const useProductBySlug = (slug: string) => {
  return useProduct(slug);
};

/**
 * Hook for fetching a single product by ID
 */
export const useProductById = (id: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.product, id],
    queryFn: () => productService.getProductById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  });
};



/**
 * Hook for fetching products by brand
 */
export const useProductsByBrand = (brandId: string, limit?: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.products, 'brand', brandId, limit],
    queryFn: () => productService.getProductsByBrand(brandId, limit),
    enabled: !!brandId,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for fetching products by category
 */
export const useProductsByCategory = (category: string, limit?: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.products, 'category', category, limit],
    queryFn: () => productService.getProductsByCategory(category, limit),
    enabled: !!category,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for fetching related products based on category and brand
 */
export const useRelatedProducts = (product: ProductWithBrand | null, limit: number = 4) => {
  return useQuery({
    queryKey: [QUERY_KEYS.relatedProducts, product?.id, limit],
    queryFn: async () => {
      if (!product) return [];

      // First try to get products from the same category
      let relatedProducts = await productService.getProductsByCategory(product.category, limit + 1);

      // Remove the current product from the results
      relatedProducts = relatedProducts.filter(p => p.id !== product.id);

      // If we don't have enough products from the same category, get more from the same brand
      if (relatedProducts.length < limit && product.brand_id) {
        const brandProducts = await productService.getProductsByBrand(product.brand_id, limit + 1);
        const filteredBrandProducts = brandProducts.filter(p =>
          p.id !== product.id && !relatedProducts.some(rp => rp.id === p.id)
        );
        relatedProducts = [...relatedProducts, ...filteredBrandProducts];
      }

      // If still not enough, get random products
      if (relatedProducts.length < limit) {
        const allProducts = await productService.getProducts({ limit: limit * 2 });
        const filteredAllProducts = allProducts.filter(p =>
          p.id !== product.id && !relatedProducts.some(rp => rp.id === p.id)
        );
        relatedProducts = [...relatedProducts, ...filteredAllProducts];
      }

      return relatedProducts.slice(0, limit);
    },
    enabled: !!product,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook for searching products
 */
export const useSearchProducts = (query: string, limit?: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.products, 'search', query, limit],
    queryFn: () => productService.searchProducts(query, limit),
    enabled: !!query && query.length > 2,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook for fetching product categories (flat list for filtering)
 */
export const useCategories = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.categories],
    queryFn: () => productService.getCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook for fetching hierarchical categories (for navigation)
 */
export const useHierarchicalCategories = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.categories, 'hierarchical'],
    queryFn: () => productService.getHierarchicalCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook for fetching brands
 */
export const useBrands = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.brands],
    queryFn: () => productService.getBrands(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};



/**
 * Hook for prefetching product data
 */
export const usePrefetchProduct = () => {
  const queryClient = useQueryClient();

  return {
    prefetchProduct: (slug: string) => {
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.product, slug],
        queryFn: () => productService.getProductBySlug(slug),
        staleTime: 10 * 60 * 1000,
      });
    },
    prefetchProducts: (params: ProductQueryParams) => {
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.products, params],
        queryFn: () => productService.getProducts(params),
        staleTime: 5 * 60 * 1000,
      });
    },
  };
};

/**
 * Hook for invalidating product cache
 */
export const useInvalidateProducts = () => {
  const queryClient = useQueryClient();

  return {
    invalidateProducts: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.products] });
    },
    invalidateProduct: (slug: string) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.product, slug] });
    },
    invalidateAll: () => {
      queryClient.invalidateQueries();
    },
  };
};

/**
 * Utility hook for search functionality
 */
export const useProductSearch = (searchTerm: string, debounceMs: number = 300) => {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState(searchTerm);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  return useSearchProducts(debouncedSearchTerm, 20);
};

// Re-export types for convenience
export type { ProductWithBrand, ProductQueryParams };
