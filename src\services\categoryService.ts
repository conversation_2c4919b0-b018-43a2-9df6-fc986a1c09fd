/**
 * Category Service for hierarchical category management
 * Handles all category-related operations with parent-child relationships
 */

import { supabase } from '@/lib/supabase'
import type { Database } from '@/lib/supabase'

type Category = Database['public']['Tables']['categories']['Row']
type CategoryInsert = Database['public']['Tables']['categories']['Insert']
type CategoryUpdate = Database['public']['Tables']['categories']['Update']

export interface CategoryWithChildren extends Category {
  children?: CategoryWithChildren[]
  parent?: Category | null
  product_count?: number
}

export interface CategoryTreeNode {
  id: string
  name: string
  slug: string
  description: string | null
  parent_id: string | null
  sort_order: number
  is_active: boolean
  children: CategoryTreeNode[]
  product_count: number
}

class CategoryService {
  /**
   * Get all categories with hierarchical structure
   */
  async getCategories(): Promise<CategoryWithChildren[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order')

    if (error) throw error

    // Build hierarchical structure
    return this.buildCategoryTree(data || [])
  }

  /**
   * Get all categories (flat list) for admin
   */
  async getAllCategories(): Promise<Category[]> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('sort_order')

      if (error) throw error

      // If no categories exist, return empty array (will be handled by fallback logic)
      return data || []
    } catch (error) {
      console.log('CategoryService - Categories table not available, returning empty array:', error)
      return []
    }
  }

  /**
   * Get category by ID with parent and children
   */
  async getCategory(id: string): Promise<CategoryWithChildren | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null
      throw error
    }

    // Get parent if exists
    let parent = null
    if (data.parent_id) {
      const { data: parentData } = await supabase
        .from('categories')
        .select('*')
        .eq('id', data.parent_id)
        .single()
      parent = parentData
    }

    // Get children
    const { data: childrenData } = await supabase
      .from('categories')
      .select('*')
      .eq('parent_id', id)
      .eq('is_active', true)
      .order('sort_order')

    return {
      ...data,
      parent,
      children: childrenData || []
    }
  }

  /**
   * Get category by slug
   */
  async getCategoryBySlug(slug: string): Promise<CategoryWithChildren | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null
      throw error
    }

    return this.getCategory(data.id)
  }

  /**
   * Get main categories (no parent)
   */
  async getMainCategories(): Promise<CategoryWithChildren[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .is('parent_id', null)
      .eq('is_active', true)
      .order('sort_order')

    if (error) throw error

    // Get children for each main category
    const categoriesWithChildren = await Promise.all(
      (data || []).map(async (category) => {
        const { data: children } = await supabase
          .from('categories')
          .select('*')
          .eq('parent_id', category.id)
          .eq('is_active', true)
          .order('sort_order')

        return {
          ...category,
          children: children || []
        }
      })
    )

    return categoriesWithChildren
  }

  /**
   * Get subcategories for a parent category
   */
  async getSubcategories(parentId: string): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('parent_id', parentId)
      .eq('is_active', true)
      .order('sort_order')

    if (error) throw error
    return data || []
  }

  /**
   * Create new category
   */
  async createCategory(category: CategoryInsert): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .insert(category)
      .select()
      .single()

    if (error) throw error
    return data
  }

  /**
   * Update category
   */
  async updateCategory(id: string, updates: CategoryUpdate): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  /**
   * Delete category (and all subcategories)
   */
  async deleteCategory(id: string): Promise<void> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  /**
   * Reorder categories
   */
  async reorderCategories(categoryOrders: { id: string; sort_order: number }[]): Promise<void> {
    const updates = categoryOrders.map(({ id, sort_order }) =>
      supabase
        .from('categories')
        .update({ sort_order, updated_at: new Date().toISOString() })
        .eq('id', id)
    )

    await Promise.all(updates)
  }

  /**
   * Get category tree for admin interface
   */
  async getCategoryTree(): Promise<CategoryTreeNode[]> {
    const categories = await this.getAllCategories()

    // Get product counts for each category
    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const { count } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true })
          .eq('category_id', category.id)

        return {
          ...category,
          product_count: count || 0
        }
      })
    )

    return this.buildTree(categoriesWithCounts)
  }

  /**
   * Build hierarchical category tree
   */
  private buildCategoryTree(categories: Category[]): CategoryWithChildren[] {
    const categoryMap = new Map<string, CategoryWithChildren>()
    const rootCategories: CategoryWithChildren[] = []

    // Create map of all categories
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] })
    })

    // Build tree structure
    categories.forEach(category => {
      const categoryNode = categoryMap.get(category.id)!

      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id)
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(categoryNode)
        }
      } else {
        rootCategories.push(categoryNode)
      }
    })

    return rootCategories
  }

  /**
   * Build tree for admin interface
   */
  private buildTree(categories: (Category & { product_count: number })[]): CategoryTreeNode[] {
    const categoryMap = new Map<string, CategoryTreeNode>()
    const rootCategories: CategoryTreeNode[] = []

    // Create map of all categories
    categories.forEach(category => {
      categoryMap.set(category.id, {
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        parent_id: category.parent_id,
        sort_order: category.sort_order,
        is_active: category.is_active,
        children: [],
        product_count: category.product_count
      })
    })

    // Build tree structure
    categories.forEach(category => {
      const categoryNode = categoryMap.get(category.id)!

      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id)
        if (parent) {
          parent.children.push(categoryNode)
        }
      } else {
        rootCategories.push(categoryNode)
      }
    })

    // Sort children by sort_order
    const sortChildren = (nodes: CategoryTreeNode[]) => {
      nodes.sort((a, b) => a.sort_order - b.sort_order)
      nodes.forEach(node => {
        if (node.children.length > 0) {
          sortChildren(node.children)
        }
      })
    }

    sortChildren(rootCategories)
    return rootCategories
  }
}

export const categoryService = new CategoryService()
export type { Category, CategoryInsert, CategoryUpdate }
