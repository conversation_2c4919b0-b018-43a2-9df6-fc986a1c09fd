-- Migration to update products table to use category_id instead of category string
-- This migration will:
-- 1. Add category_id column to products table
-- 2. Update products to reference the new categories (categories should be created via seeds)

-- Step 1: Add category_id column to products table (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id);
    END IF;
END $$;

-- Step 2: Update products to use category_id based on their current category string
-- Note: Categories should be created first via the seeds/002_categories_data.sql file

-- Step 3: Update products to use category_id based on their current category string
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'AHU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'HRV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'ERV' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'FCU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'Ecology Unit' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Condensing Unit' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440041' WHERE category = 'Water Source Heat Pump' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-446655440007' WHERE category = 'Exhaust Unit' AND category_id IS NULL;

-- Create indexes for better performance
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug ON categories(slug);
