/**
 * Admin Setup and Debug Page
 * Helps troubleshoot admin authentication and database issues
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertCircle, Database, User, Key } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { cmsService } from '@/services/cmsService';

const AdminSetup = () => {
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [dbTables, setDbTables] = useState<string[]>([]);
  const [brandsCount, setBrandsCount] = useState<number>(0);
  const [productsCount, setProductsCount] = useState<number>(0);
  const [authStatus, setAuthStatus] = useState<'checking' | 'authenticated' | 'not-authenticated'>('checking');
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [createUserEmail, setCreateUserEmail] = useState('');
  const [createUserPassword, setCreateUserPassword] = useState('');
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  useEffect(() => {
    checkConnection();
    checkAuth();
  }, []);

  const checkConnection = async () => {
    try {
      setConnectionStatus('checking');
      
      // Test database connection
      const { data, error } = await supabase.from('brands').select('count', { count: 'exact', head: true });
      
      if (error) {
        console.error('Database connection error:', error);
        setConnectionStatus('error');
        return;
      }

      setConnectionStatus('connected');
      
      // Get table info
      const brands = await cmsService.getBrands();
      const products = await cmsService.getProducts({ limit: 1 });
      
      setBrandsCount(brands.length);
      setProductsCount(products.count);
      
    } catch (error) {
      console.error('Connection check failed:', error);
      setConnectionStatus('error');
    }
  };

  const checkAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.user) {
        setAuthStatus('authenticated');
        setCurrentUser(session.user);
      } else {
        setAuthStatus('not-authenticated');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setAuthStatus('not-authenticated');
    }
  };

  const createAdminUser = async () => {
    if (!createUserEmail || !createUserPassword) {
      setMessage({ type: 'error', text: 'Please enter both email and password' });
      return;
    }

    setIsCreatingUser(true);
    setMessage(null);

    try {
      const { data, error } = await supabase.auth.signUp({
        email: createUserEmail,
        password: createUserPassword,
      });

      if (error) {
        setMessage({ type: 'error', text: error.message });
      } else {
        setMessage({ 
          type: 'success', 
          text: 'Admin user created successfully! Check your email for verification link.' 
        });
        setCreateUserEmail('');
        setCreateUserPassword('');
        checkAuth();
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to create admin user' });
    } finally {
      setIsCreatingUser(false);
    }
  };

  const signInUser = async () => {
    if (!createUserEmail || !createUserPassword) {
      setMessage({ type: 'error', text: 'Please enter both email and password' });
      return;
    }

    setIsCreatingUser(true);
    setMessage(null);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: createUserEmail,
        password: createUserPassword,
      });

      if (error) {
        setMessage({ type: 'error', text: error.message });
      } else {
        setMessage({ type: 'success', text: 'Signed in successfully!' });
        checkAuth();
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to sign in' });
    } finally {
      setIsCreatingUser(false);
    }
  };

  const StatusIcon = ({ status }: { status: 'checking' | 'connected' | 'authenticated' | 'error' | 'not-authenticated' }) => {
    switch (status) {
      case 'connected':
      case 'authenticated':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
      case 'not-authenticated':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'checking':
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Setup & Debug</h1>
          <p className="text-gray-600">Troubleshoot admin authentication and database connectivity</p>
        </div>

        {message && (
          <Alert className={message.type === 'error' ? 'border-red-500' : message.type === 'success' ? 'border-green-500' : 'border-blue-500'}>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        {/* Database Connection Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Connection
              <StatusIcon status={connectionStatus} />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Status:</strong> {connectionStatus}</p>
              <p><strong>Brands in database:</strong> {brandsCount}</p>
              <p><strong>Products in database:</strong> {productsCount}</p>
              <Button onClick={checkConnection} variant="outline" size="sm">
                Refresh Connection
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Authentication Status
              <StatusIcon status={authStatus} />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Status:</strong> {authStatus}</p>
              {currentUser && (
                <div>
                  <p><strong>User Email:</strong> {currentUser.email}</p>
                  <p><strong>User ID:</strong> {currentUser.id}</p>
                </div>
              )}
              <Button onClick={checkAuth} variant="outline" size="sm">
                Refresh Auth Status
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Create/Sign In Admin User */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Admin User Management
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={createUserEmail}
                  onChange={(e) => setCreateUserEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={createUserPassword}
                  onChange={(e) => setCreateUserPassword(e.target.value)}
                  placeholder="Enter password"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button 
                onClick={createAdminUser} 
                disabled={isCreatingUser}
                variant="default"
              >
                {isCreatingUser ? 'Creating...' : 'Create Admin User'}
              </Button>
              <Button 
                onClick={signInUser} 
                disabled={isCreatingUser}
                variant="outline"
              >
                {isCreatingUser ? 'Signing In...' : 'Sign In'}
              </Button>
            </div>

            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>Instructions:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>If you don't have an admin user, click "Create Admin User"</li>
                <li>If you already have an admin user, click "Sign In"</li>
                <li>After creating a user, check your email for verification</li>
                <li>Once authenticated, you can access the admin dashboard</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              onClick={() => window.location.href = '/admin/login'} 
              className="w-full"
            >
              Go to Admin Login
            </Button>
            <Button 
              onClick={() => window.location.href = '/admin'} 
              variant="outline" 
              className="w-full"
            >
              Go to Admin Dashboard
            </Button>
            <Button 
              onClick={() => window.location.href = '/'} 
              variant="outline" 
              className="w-full"
            >
              Back to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminSetup;
