-- Simple Category Setup Script
-- This script will fix the "No products found" and "No subcategories" issues
-- Run this in your Supabase SQL Editor

-- Step 1: Create categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Add category_id column to products table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id);
    END IF;
END $$;

-- Step 3: Clear existing categories to avoid conflicts
DELETE FROM categories;

-- Step 4: Insert main categories first
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
('550e8400-e29b-41d4-a716-************', 'Air Handling Units', 'air-handling-units', 'Complete air handling and treatment systems', NULL, 1, true),
('550e8400-e29b-41d4-a716-************', 'Heat Recovery Systems', 'heat-recovery-systems', 'Energy efficient heat recovery systems', NULL, 2, true),
('550e8400-e29b-41d4-a716-************', 'Fan Coil Units', 'fan-coil-units', 'Compact heating and cooling units', NULL, 3, true),
('550e8400-e29b-41d4-a716-************', 'Cooling Systems', 'cooling-systems', 'Precision cooling and air conditioning', NULL, 4, true),
('550e8400-e29b-41d4-a716-************', 'Condensing Units', 'condensing-units', 'Outdoor condensing units', NULL, 5, true),
('550e8400-e29b-41d4-a716-************', 'Heat Pumps', 'heat-pumps', 'Energy efficient heat pump systems', NULL, 6, true),
('550e8400-e29b-41d4-a716-446655440007', 'Exhaust Systems', 'exhaust-systems', 'Ventilation and exhaust systems', NULL, 7, true),
('550e8400-e29b-41d4-a716-446655440008', 'Electrical Enclosures', 'electrical-enclosures', 'Electrical protection solutions', NULL, 8, true);

-- Step 5: Insert subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
-- Air Handling Unit subcategories
('650e8400-e29b-41d4-a716-446655440011', 'AHU General Features', 'ahu-general-features', 'Standard air handling units', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440012', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems', '550e8400-e29b-41d4-a716-************', 2, true),
('650e8400-e29b-41d4-a716-446655440013', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Clean room air handlers', '550e8400-e29b-41d4-a716-************', 3, true),
('650e8400-e29b-41d4-a716-446655440014', 'Packaged Hygienic AHU', 'packaged-hygienic-ahu', 'Pre-assembled hygienic systems', '550e8400-e29b-41d4-a716-************', 4, true),
('650e8400-e29b-41d4-a716-446655440015', 'AHU With Heat Recovery', 'ahu-with-heat-recovery', 'Air handlers with heat recovery', '550e8400-e29b-41d4-a716-************', 5, true),
('650e8400-e29b-41d4-a716-446655440016', 'Pool Dehumidification Unit', 'pool-dehumidification-unit', 'Pool environment systems', '550e8400-e29b-41d4-a716-************', 6, true),

-- Heat Recovery subcategories
('650e8400-e29b-41d4-a716-446655440021', 'Heat Recovery Ventilation', 'heat-recovery-ventilation', 'HRV systems', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440022', 'Energy Recovery Ventilation', 'energy-recovery-ventilation', 'ERV systems', '550e8400-e29b-41d4-a716-************', 2, true),

-- Cooling System subcategories
('650e8400-e29b-41d4-a716-446655440031', 'Precision Air Conditioning', 'precision-air-conditioning', 'High-precision cooling systems', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440032', 'Ecology Units', 'ecology-units', 'Environmental control systems', '550e8400-e29b-41d4-a716-************', 2, true),

-- Heat Pump subcategories
('650e8400-e29b-41d4-a716-446655440041', 'Water Source Heat Pumps', 'water-source-heat-pumps', 'Water source heat pumps', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440042', 'Air Source Heat Pumps', 'air-source-heat-pumps', 'Air source heat pumps', '550e8400-e29b-41d4-a716-************', 2, true);

-- Step 6: Update existing products to use category_id
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440011' WHERE category = 'AHU';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440021' WHERE category = 'HRV';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440022' WHERE category = 'ERV';
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'FCU';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440032' WHERE category = 'Ecology Unit';
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Condensing Unit';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440041' WHERE category = 'Water Source Heat Pump';
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-446655440007' WHERE category = 'Exhaust Unit';

-- Step 7: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Step 8: Verification and debugging
SELECT 'Categories created:' as info, count(*) as count FROM categories;
SELECT 'Products with category_id:' as info, count(*) as count FROM products WHERE category_id IS NOT NULL;
SELECT 'Products without category_id:' as info, count(*) as count FROM products WHERE category_id IS NULL;

-- Show all categories created
SELECT 'All categories:' as info;
SELECT id, name, slug, parent_id, is_active FROM categories ORDER BY sort_order;

-- Show all products and their categories
SELECT 'All products:' as info;
SELECT id, name, category, category_id FROM products;

-- Success message
SELECT 'Category setup completed successfully! Check the results above.' as message;
