import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Heart,
  FlaskConical,
  Briefcase,
  UtensilsCrossed,
  Store,
  CheckCircle,
  ArrowRight,
  Star,
  Users,
  Award,
  Clock,
  Phone,
  Mail,
  MapPin,
  Calendar,
  TrendingUp,
  Shield,
  Zap,
  Settings
} from 'lucide-react';
import { useParams, Link } from 'react-router-dom';
import { useEffect } from 'react';

const Solutions = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  const { category } = useParams();

  const solutionsData = {
    hospitality: {
      icon: <Building2 className="h-12 w-12" />,
      title: "Hospitality Solutions",
      description: "Complete MEP solutions for hotels, resorts, and hospitality venues with focus on guest comfort and energy efficiency.",
      features: ["Guest Room Climate Control", "Central HVAC Systems", "Kitchen Ventilation", "Pool & Spa Systems", "Emergency Systems"],
      projects: "50+ Hotels",
      detailedDescription: "Our hospitality MEP solutions are designed to create exceptional guest experiences while optimizing operational efficiency. We understand the unique challenges of the hospitality industry, from maintaining consistent comfort across diverse spaces to managing energy costs and ensuring reliable operations 24/7.",
      services: [
        {
          title: "Guest Room Climate Control",
          description: "Individual room temperature and humidity control systems for optimal guest comfort",
          icon: <Settings className="h-6 w-6" />
        },
        {
          title: "Central HVAC Systems",
          description: "Energy-efficient central air conditioning and heating systems for common areas",
          icon: <Zap className="h-6 w-6" />
        },
        {
          title: "Kitchen Ventilation",
          description: "Commercial kitchen exhaust and make-up air systems meeting health codes",
          icon: <UtensilsCrossed className="h-6 w-6" />
        },
        {
          title: "Pool & Spa Systems",
          description: "Complete mechanical systems for pools, spas, and wellness facilities",
          icon: <Heart className="h-6 w-6" />
        }
      ],
      caseStudies: [
        {
          title: "Grand Plaza Hotel Cairo",
          location: "Cairo, Egypt",
          description: "300-room luxury hotel with complete MEP installation including smart room controls and energy management systems.",
          results: ["30% energy savings", "99.9% system uptime", "Enhanced guest satisfaction"],
          image: "/placeholder.svg"
        },
        {
          title: "Red Sea Resort",
          location: "Hurghada, Egypt",
          description: "Beachfront resort with specialized HVAC systems designed for coastal environments and high humidity.",
          results: ["Corrosion-resistant systems", "Optimal indoor air quality", "Reduced maintenance costs"],
          image: "/placeholder.svg"
        }
      ],
      stats: [
        { number: "50+", label: "Hotels Completed" },
        { number: "15,000+", label: "Guest Rooms" },
        { number: "99.9%", label: "System Uptime" },
        { number: "30%", label: "Energy Savings" }
      ]
    },
    healthcare: {
      icon: <Heart className="h-12 w-12" />,
      title: "Healthcare Solutions",
      description: "Specialized MEP installations for hospitals, clinics, and medical facilities meeting strict health and safety standards.",
      features: ["Medical Gas Systems", "Clean Room HVAC", "Emergency Power", "Infection Control", "Operating Room Systems"],
      projects: "25+ Medical Facilities",
      detailedDescription: "Healthcare facilities require the highest standards of MEP systems to ensure patient safety, staff comfort, and regulatory compliance. Our healthcare solutions are designed with redundancy, reliability, and precision to support critical medical operations.",
      services: [
        {
          title: "Medical Gas Systems",
          description: "Complete medical gas distribution systems including oxygen, nitrous oxide, and vacuum",
          icon: <FlaskConical className="h-6 w-6" />
        },
        {
          title: "Clean Room HVAC",
          description: "Specialized HVAC systems for operating rooms and sterile environments",
          icon: <Shield className="h-6 w-6" />
        },
        {
          title: "Emergency Power Systems",
          description: "Uninterruptible power supply and backup generators for critical operations",
          icon: <Zap className="h-6 w-6" />
        },
        {
          title: "Infection Control",
          description: "Advanced air filtration and pressure control systems to prevent contamination",
          icon: <Heart className="h-6 w-6" />
        }
      ],
      caseStudies: [
        {
          title: "Cairo Medical Center",
          location: "Cairo, Egypt",
          description: "500-bed hospital with complete MEP systems including 12 operating rooms and ICU facilities.",
          results: ["Zero system failures", "100% regulatory compliance", "Improved patient outcomes"],
          image: "/placeholder.svg"
        }
      ],
      stats: [
        { number: "25+", label: "Medical Facilities" },
        { number: "100+", label: "Operating Rooms" },
        { number: "99.9%", label: "System Uptime" },
        { number: "24/7", label: "Support Available" }
      ]
    },
    pharmaceutical: {
      icon: <FlaskConical className="h-12 w-12" />,
      title: "Pharmaceutical Solutions",
      description: "Precision MEP systems for pharmaceutical manufacturing with controlled environments and regulatory compliance.",
      features: ["Clean Room Design", "Process Cooling", "Compressed Air Systems", "Waste Management", "Validation Support"],
      projects: "15+ Pharma Plants",
      detailedDescription: "Pharmaceutical manufacturing requires the most stringent environmental controls and regulatory compliance. Our pharmaceutical MEP solutions ensure product quality, worker safety, and regulatory adherence through precision-engineered systems.",
      services: [
        {
          title: "Clean Room Design",
          description: "ISO-certified clean room environments with precise temperature, humidity, and particle control",
          icon: <Shield className="h-6 w-6" />
        },
        {
          title: "Process Cooling",
          description: "Specialized cooling systems for pharmaceutical manufacturing processes",
          icon: <Zap className="h-6 w-6" />
        },
        {
          title: "Compressed Air Systems",
          description: "Oil-free compressed air systems meeting pharmaceutical grade requirements",
          icon: <Settings className="h-6 w-6" />
        },
        {
          title: "Validation Support",
          description: "Complete documentation and validation support for regulatory compliance",
          icon: <CheckCircle className="h-6 w-6" />
        }
      ],
      caseStudies: [
        {
          title: "Pharma Manufacturing Plant",
          location: "Alexandria, Egypt",
          description: "State-of-the-art pharmaceutical manufacturing facility with ISO 14644 clean rooms.",
          results: ["FDA compliance achieved", "Zero contamination incidents", "Reduced production costs"],
          image: "/placeholder.svg"
        }
      ],
      stats: [
        { number: "15+", label: "Pharma Plants" },
        { number: "100%", label: "FDA Compliance" },
        { number: "ISO 14644", label: "Clean Room Standard" },
        { number: "0", label: "Contamination Events" }
      ]
    },
    business: {
      icon: <Briefcase className="h-12 w-12" />,
      title: "Business Solutions",
      description: "Modern MEP solutions for office buildings, corporate headquarters, and business complexes.",
      features: ["Smart Building Systems", "Energy Management", "Conference Room AV", "Security Integration", "Flexible Workspaces"],
      projects: "100+ Office Buildings",
      detailedDescription: "Modern business environments require intelligent, efficient, and flexible MEP systems that adapt to changing needs while maintaining optimal comfort and productivity for occupants.",
      services: [
        {
          title: "Smart Building Systems",
          description: "Integrated building automation systems for optimal comfort and efficiency",
          icon: <Settings className="h-6 w-6" />
        },
        {
          title: "Energy Management",
          description: "Advanced energy monitoring and optimization systems to reduce operational costs",
          icon: <Zap className="h-6 w-6" />
        },
        {
          title: "Flexible Workspaces",
          description: "Adaptable MEP systems supporting modern flexible office layouts",
          icon: <Users className="h-6 w-6" />
        }
      ],
      caseStudies: [
        {
          title: "Corporate Tower Complex",
          location: "New Cairo, Egypt",
          description: "40-story office tower with smart building systems and energy-efficient MEP design.",
          results: ["40% energy reduction", "LEED Gold certification", "Improved tenant satisfaction"],
          image: "/placeholder.svg"
        }
      ],
      stats: [
        { number: "100+", label: "Office Buildings" },
        { number: "40%", label: "Energy Reduction" },
        { number: "95%", label: "Tenant Satisfaction" },
        { number: "LEED Gold", label: "Certification" }
      ]
    },
    "food-beverage": {
      icon: <UtensilsCrossed className="h-12 w-12" />,
      title: "Food & Beverage Solutions",
      description: "Specialized MEP systems for food processing, restaurants, and beverage manufacturing facilities.",
      features: ["Food Grade Systems", "Cold Storage", "Process Ventilation", "Waste Water Treatment", "Hygiene Systems"],
      projects: "30+ F&B Facilities",
      detailedDescription: "Food and beverage facilities require specialized MEP systems that meet strict hygiene standards, maintain precise environmental conditions, and ensure product safety throughout the production process.",
      services: [
        {
          title: "Food Grade Systems",
          description: "Stainless steel piping and food-safe materials throughout all systems",
          icon: <Shield className="h-6 w-6" />
        },
        {
          title: "Cold Storage Solutions",
          description: "Precision temperature control for refrigeration and freezer facilities",
          icon: <Zap className="h-6 w-6" />
        },
        {
          title: "Process Ventilation",
          description: "Specialized ventilation systems for food processing environments",
          icon: <Settings className="h-6 w-6" />
        }
      ],
      caseStudies: [
        {
          title: "Food Processing Plant",
          location: "10th of Ramadan, Egypt",
          description: "Large-scale food processing facility with complete cold chain management systems.",
          results: ["Zero food safety incidents", "30% energy savings", "HACCP compliance"],
          image: "/placeholder.svg"
        }
      ],
      stats: [
        { number: "30+", label: "F&B Facilities" },
        { number: "100%", label: "HACCP Compliance" },
        { number: "0", label: "Safety Incidents" },
        { number: "30%", label: "Energy Savings" }
      ]
    },
    commercial: {
      icon: <Store className="h-12 w-12" />,
      title: "Commercial Solutions",
      description: "Comprehensive MEP solutions for retail spaces, shopping centers, and commercial developments.",
      features: ["Retail HVAC", "Lighting Design", "Fire Safety", "Escalator Systems", "Parking Ventilation"],
      projects: "75+ Commercial Projects",
      detailedDescription: "Commercial spaces require MEP systems that create comfortable shopping environments, ensure safety, and operate efficiently to support business success while managing operational costs.",
      services: [
        {
          title: "Retail HVAC",
          description: "Specialized HVAC systems designed for retail environments and customer comfort",
          icon: <Settings className="h-6 w-6" />
        },
        {
          title: "Lighting Design",
          description: "Energy-efficient lighting systems that enhance product displays and ambiance",
          icon: <Zap className="h-6 w-6" />
        },
        {
          title: "Fire Safety Systems",
          description: "Comprehensive fire detection, suppression, and evacuation systems",
          icon: <Shield className="h-6 w-6" />
        }
      ],
      caseStudies: [
        {
          title: "City Center Mall",
          location: "Cairo, Egypt",
          description: "Large shopping center with comprehensive MEP systems serving 200+ retail outlets.",
          results: ["Optimal shopping comfort", "25% energy savings", "Enhanced safety systems"],
          image: "/placeholder.svg"
        }
      ],
      stats: [
        { number: "75+", label: "Commercial Projects" },
        { number: "200+", label: "Retail Outlets" },
        { number: "25%", label: "Energy Savings" },
        { number: "100%", label: "Safety Compliance" }
      ]
    }
  };

  const solutions = [
    {
      icon: <Building2 className="h-12 w-12" />,
      title: "Hospitality",
      description: "Complete MEP solutions for hotels, resorts, and hospitality venues with focus on guest comfort and energy efficiency.",
      features: ["Guest Room Climate Control", "Central HVAC Systems", "Kitchen Ventilation", "Pool & Spa Systems", "Emergency Systems"],
      projects: "50+ Hotels",
      href: "/solutions/hospitality"
    },
    {
      icon: <Heart className="h-12 w-12" />,
      title: "Healthcare",
      description: "Specialized MEP installations for hospitals, clinics, and medical facilities meeting strict health and safety standards.",
      features: ["Medical Gas Systems", "Clean Room HVAC", "Emergency Power", "Infection Control", "Operating Room Systems"],
      projects: "25+ Medical Facilities",
      href: "/solutions/healthcare"
    },
    {
      icon: <FlaskConical className="h-12 w-12" />,
      title: "Pharmaceutical",
      description: "Precision MEP systems for pharmaceutical manufacturing with controlled environments and regulatory compliance.",
      features: ["Clean Room Design", "Process Cooling", "Compressed Air Systems", "Waste Management", "Validation Support"],
      projects: "15+ Pharma Plants",
      href: "/solutions/pharmaceutical"
    },
    {
      icon: <Briefcase className="h-12 w-12" />,
      title: "Business",
      description: "Modern MEP solutions for office buildings, corporate headquarters, and business complexes.",
      features: ["Smart Building Systems", "Energy Management", "Conference Room AV", "Security Integration", "Flexible Workspaces"],
      projects: "100+ Office Buildings",
      href: "/solutions/business"
    },
    {
      icon: <UtensilsCrossed className="h-12 w-12" />,
      title: "Food and Beverage",
      description: "Specialized MEP systems for food processing, restaurants, and beverage manufacturing facilities.",
      features: ["Food Grade Systems", "Cold Storage", "Process Ventilation", "Waste Water Treatment", "Hygiene Systems"],
      projects: "30+ F&B Facilities",
      href: "/solutions/food-beverage"
    },
    {
      icon: <Store className="h-12 w-12" />,
      title: "Commercial",
      description: "Comprehensive MEP solutions for retail spaces, shopping centers, and commercial developments.",
      features: ["Retail HVAC", "Lighting Design", "Fire Safety", "Escalator Systems", "Parking Ventilation"],
      projects: "75+ Commercial Projects",
      href: "/solutions/commercial"
    }
  ];

  // If category is specified, show detailed category page
  if (category && solutionsData[category as keyof typeof solutionsData]) {
    const solutionData = solutionsData[category as keyof typeof solutionsData];

    return (
      <div className="min-h-screen overflow-x-hidden">
        <Navigation />

        {/* Category Hero Section */}
        <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className="w-24 h-24 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <div className="text-white">
                  {solutionData.icon}
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
                {solutionData.title}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
                {solutionData.detailedDescription}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-gradient-primary hover:opacity-90 shadow-primary"
                  onClick={() => {
                    // Navigate to contact page
                    window.location.href = '/contact';
                  }}
                >
                  Request Consultation
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-primary text-primary hover:bg-primary hover:text-white"
                  onClick={() => {
                    // Download solution brochure
                    const link = document.createElement('a');
                    link.href = `/downloads/${solutionData.title.toLowerCase().replace(/\s+/g, '-')}-brochure.pdf`;
                    link.download = `${solutionData.title}-Solutions-Brochure.pdf`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                >
                  Download Brochure
                </Button>
              </div>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {solutionData.stats.map((stat, index) => (
                <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300">
                  <CardContent className="p-6">
                    <h3 className="text-3xl font-bold text-primary mb-2">{stat.number}</h3>
                    <p className="text-muted-foreground font-medium">{stat.label}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-background">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
                Our <span className="text-primary">Services</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Comprehensive MEP solutions tailored to your industry's specific requirements
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {solutionData.services.map((service, index) => (
                <Card key={index} className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                      <div className="text-white">
                        {service.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-foreground mb-4">{service.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{service.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>



        {/* Call to Action */}
        <section className="py-20 bg-gradient-to-br from-primary/5 to-accent/5">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Ready to Start Your <span className="text-primary">Project?</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Contact our experts today to discuss your {solutionData.title.toLowerCase()} requirements
              and discover how we can help you achieve your goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-primary hover:opacity-90 shadow-primary"
                onClick={() => {
                  // Call the phone number
                  window.location.href = 'tel:+201281008799';
                }}
              >
                <Phone className="mr-2 h-5 w-5" />
                Call Now
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-primary text-primary hover:bg-primary hover:text-white"
                onClick={() => {
                  // Navigate to contact page
                  window.location.href = '/contact';
                }}
              >
                <Mail className="mr-2 h-5 w-5" />
                Send Message
              </Button>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    );
  }

  // Default solutions overview page
  const benefits = [
    "Industry-specific expertise and compliance",
    "Energy-efficient and sustainable solutions",
    "24/7 maintenance and support services",
    "Advanced building automation systems",
    "Regulatory compliance and certifications",
    "Cost-effective lifecycle management"
  ];

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Industry Solutions
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Specialized MEP solutions tailored to meet the unique requirements of different industries,
              ensuring optimal performance, compliance, and efficiency.
            </p>
          </div>
        </div>
      </section>

      {/* Solutions Grid */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {solutions.map((solution, index) => (
              <Card key={index} className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] border-border">
                <CardHeader className="text-center pb-4">
                  <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <div className="text-white">
                      {solution.icon}
                    </div>
                  </div>
                  <CardTitle className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
                    {solution.title}
                  </CardTitle>
                  <Badge variant="secondary" className="w-fit mx-auto">
                    {solution.projects}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground text-center">
                    {solution.description}
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-foreground">Key Features:</h4>
                    <ul className="space-y-1">
                      {solution.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start text-sm text-muted-foreground">
                          <CheckCircle className="h-4 w-4 text-primary mr-2 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Link to={solution.href}>
                    <Button className="w-full bg-gradient-primary hover:opacity-90 shadow-primary group min-h-[44px]">
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Why Choose Our <span className="text-primary">Solutions</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Our industry-specific approach ensures that every MEP system is designed and implemented
              to meet the unique challenges and requirements of your sector.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center space-x-3 p-4 bg-background rounded-lg border border-border">
                <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                <span className="text-foreground font-medium">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Solutions;
