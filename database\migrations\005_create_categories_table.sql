-- Create categories table with hierarchical support
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
CREATE INDEX idx_categories_active ON categories(is_active);

-- Create trigger for updated_at
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Enable read access for all users" ON categories FOR SELECT USING (is_active = true);
CREATE POLICY "Enable all access for authenticated users" ON categories FOR ALL USING (auth.role() = 'authenticated');

-- Update products table to use category_id instead of category string
ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id);

-- Create index for the new foreign key
CREATE INDEX idx_products_category_id ON products(category_id);

-- Insert initial categories with hierarchical structure
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
-- Main Categories
('550e8400-e29b-41d4-a716-************', 'Air Handling Unit', 'air-handling-unit', 'Complete air handling solutions for HVAC systems', NULL, 1),
('550e8400-e29b-41d4-a716-************', 'Condensing Unit', 'condensing-unit', 'Refrigeration condensing units and components', NULL, 2),
('550e8400-e29b-41d4-a716-************', 'Heat Recovery Ventilation', 'heat-recovery-ventilation', 'Energy efficient ventilation systems', NULL, 3),
('550e8400-e29b-41d4-a716-************', 'Fan Coil Unit', 'fan-coil-unit', 'Fan coil units for heating and cooling', NULL, 4),
('550e8400-e29b-41d4-a716-************', 'Heat Pump Systems', 'heat-pump-systems', 'Heat pump solutions for various applications', NULL, 5),
('550e8400-e29b-41d4-a716-446655440106', 'Exhaust Systems', 'exhaust-systems', 'Exhaust fans and ventilation systems', NULL, 6);

-- Air Handling Unit Subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440201', 'AHU General Features', 'ahu-general-features', 'Standard air handling unit features and components', '550e8400-e29b-41d4-a716-************', 1),
('550e8400-e29b-41d4-a716-446655440202', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems and fresh air units', '550e8400-e29b-41d4-a716-************', 2),
('550e8400-e29b-41d4-a716-446655440203', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Cleanroom and pharmaceutical grade air handling units', '550e8400-e29b-41d4-a716-************', 3),
('550e8400-e29b-41d4-a716-446655440204', 'Packaged Hygienic AHU', 'packaged-hygienic-ahu', 'Pre-engineered hygienic air handling solutions', '550e8400-e29b-41d4-a716-************', 4),
('550e8400-e29b-41d4-a716-446655440205', 'AHU With Heat Recovery', 'ahu-with-heat-recovery', 'Energy recovery air handling units', '550e8400-e29b-41d4-a716-************', 5),
('550e8400-e29b-41d4-a716-446655440206', 'Pool Dehumidification Unit', 'pool-dehumidification-unit', 'Specialized units for pool and spa environments', '550e8400-e29b-41d4-a716-************', 6);

-- Heat Recovery Ventilation Subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440301', 'HRV Systems', 'hrv-systems', 'Heat Recovery Ventilation systems', '550e8400-e29b-41d4-a716-************', 1),
('550e8400-e29b-41d4-a716-446655440302', 'ERV Systems', 'erv-systems', 'Energy Recovery Ventilation systems', '550e8400-e29b-41d4-a716-************', 2);

-- Fan Coil Unit Subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440401', 'Wall Mounted FCU', 'wall-mounted-fcu', 'Wall mounted fan coil units', '550e8400-e29b-41d4-a716-************', 1),
('550e8400-e29b-41d4-a716-446655440402', 'Ceiling Mounted FCU', 'ceiling-mounted-fcu', 'Ceiling mounted fan coil units', '550e8400-e29b-41d4-a716-************', 2),
('550e8400-e29b-41d4-a716-446655440403', 'Floor Standing FCU', 'floor-standing-fcu', 'Floor standing fan coil units', '550e8400-e29b-41d4-a716-************', 3);

-- Heat Pump Systems Subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440501', 'Water Source Heat Pump', 'water-source-heat-pump', 'Water source heat pump systems', '550e8400-e29b-41d4-a716-************', 1),
('550e8400-e29b-41d4-a716-446655440502', 'Air Source Heat Pump', 'air-source-heat-pump', 'Air source heat pump systems', '550e8400-e29b-41d4-a716-************', 2),
('550e8400-e29b-41d4-a716-446655440503', 'Geothermal Heat Pump', 'geothermal-heat-pump', 'Ground source heat pump systems', '550e8400-e29b-41d4-a716-************', 3);

-- Exhaust Systems Subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440601', 'Exhaust Unit', 'exhaust-unit', 'General exhaust units and fans', '550e8400-e29b-41d4-a716-446655440106', 1),
('550e8400-e29b-41d4-a716-446655440602', 'Kitchen Exhaust', 'kitchen-exhaust', 'Commercial kitchen exhaust systems', '550e8400-e29b-41d4-a716-446655440106', 2),
('550e8400-e29b-41d4-a716-446655440603', 'Industrial Exhaust', 'industrial-exhaust', 'Industrial exhaust and fume extraction', '550e8400-e29b-41d4-a716-446655440106', 3);

-- Ecology Unit (standalone category)
INSERT INTO categories (id, name, slug, description, parent_id, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440107', 'Ecology Unit', 'ecology-unit', 'Eco-friendly and sustainable HVAC solutions', NULL, 7);
