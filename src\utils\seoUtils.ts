import { ProductWithBrand } from '@/services/productService';

export const generateProductSEO = (product: ProductWithBrand) => {
  const title = `${product.name} - Nile Pro MEP Solutions`;

  const description = `${product.description || 'Professional MEP solution'}. High-quality ${product.category} equipment by ${product.brand?.name || 'leading manufacturer'}. Professional MEP installation and solutions by Nile Pro.`;

  const keywords = [
    product.name,
    product.category,
    product.brand?.name || '',
    'MEP',
    'HVAC',
    'Engineering',
    'Installation',
    'Nile Pro'
  ].filter(Boolean).join(', ');

  const image = product.images?.[0] || '/og-image.jpg';

  const url = `${window.location.origin}/products/${product.slug}`;

  return {
    title,
    description: description.length > 160 ? description.substring(0, 157) + '...' : description,
    keywords,
    image,
    url,
    type: 'product' as const
  };
};

export const generateCategorySEO = (categoryName: string, productCount: number) => {
  const title = `${categoryName} Products - Nile Pro MEP Solutions`;

  const description = `Explore our ${productCount} ${categoryName.toLowerCase()} products. Professional MEP solutions with advanced technology and durable design by Nile Pro.`;

  const keywords = [
    categoryName,
    'MEP Products',
    'HVAC',
    'Engineering Solutions',
    'Nile Pro',
    'Commercial',
    'Industrial'
  ].join(', ');

  return {
    title,
    description,
    keywords,
    image: '/og-image.jpg',
    url: `${window.location.origin}/products?category=${categoryName.toLowerCase().replace(' ', '-')}`,
    type: 'website' as const
  };
};

export const generateSearchSEO = (query: string, resultCount: number) => {
  const title = `Search Results for "${query}" - Nile Pro MEP`;

  const description = `Found ${resultCount} MEP products matching "${query}". Browse our comprehensive range of HVAC and engineering solutions.`;

  const keywords = [
    query,
    'MEP Search',
    'Product Search',
    'HVAC Products',
    'Nile Pro'
  ].join(', ');

  return {
    title,
    description,
    keywords,
    image: '/og-image.jpg',
    url: `${window.location.origin}/products?search=${encodeURIComponent(query)}`,
    type: 'website' as const
  };
};
