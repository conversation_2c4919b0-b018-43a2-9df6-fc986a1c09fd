-- Fix Product Category Mapping
-- This script will properly map products to their correct categories

-- Step 1: Check current product mappings
SELECT 'Current Product Mappings:' as info;
SELECT p.name as product_name, p.category as old_category, c.name as current_category_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
ORDER BY p.name;

-- Step 2: Fix the product mappings with more specific logic
-- Map "Air Handling Unit" product to AHU General Features
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440011' 
WHERE name ILIKE '%Air Handling Unit%' AND name NOT ILIKE '%Heat Recovery%' AND category_id IS NULL;

-- Map Heat Recovery products to Heat Recovery category
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440021' 
WHERE (name ILIKE '%Heat Recovery%' OR name ILIKE '%HRV%') AND category_id != '650e8400-e29b-41d4-a716-446655440021';

-- Map Energy Recovery products to ERV category
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440022' 
WHERE (name ILIKE '%Energy Recovery%' OR name ILIKE '%ERV%') AND category_id != '650e8400-e29b-41d4-a716-446655440022';

-- Map Pool Dehumidification to correct subcategory
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440016' 
WHERE name ILIKE '%Pool%' AND category_id != '650e8400-e29b-41d4-a716-446655440016';

-- Map Fan Coil Units
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440031' 
WHERE (name ILIKE '%Fan Coil%' OR name ILIKE '%FCU%') AND category_id IS NULL;

-- Map Water Source Heat Pumps
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440051' 
WHERE name ILIKE '%Water Source Heat Pump%' AND category_id != '650e8400-e29b-41d4-a716-446655440051';

-- Map Ecology Units
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440042' 
WHERE name ILIKE '%Ecology%' AND category_id != '650e8400-e29b-41d4-a716-446655440042';

-- Map Exhaust Units
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440061' 
WHERE name ILIKE '%Exhaust%' AND category_id != '650e8400-e29b-41d4-a716-446655440061';

-- Step 3: Handle any remaining unmapped products based on their old category field
UPDATE products 
SET category_id = CASE 
    WHEN category = 'AHU' THEN '650e8400-e29b-41d4-a716-446655440011'
    WHEN category = 'HRV' THEN '650e8400-e29b-41d4-a716-446655440021'
    WHEN category = 'ERV' THEN '650e8400-e29b-41d4-a716-446655440022'
    WHEN category = 'FCU' THEN '650e8400-e29b-41d4-a716-446655440031'
    WHEN category = 'Ecology Unit' THEN '650e8400-e29b-41d4-a716-446655440042'
    WHEN category = 'Condensing Unit' THEN '550e8400-e29b-41d4-a716-446655440005'
    WHEN category = 'Water Source Heat Pump' THEN '650e8400-e29b-41d4-a716-446655440051'
    WHEN category = 'Exhaust Unit' THEN '650e8400-e29b-41d4-a716-446655440061'
    ELSE category_id
END
WHERE category_id IS NULL AND category IS NOT NULL;

-- Step 4: For any remaining unmapped products, assign them to a default category based on their name
UPDATE products 
SET category_id = '650e8400-e29b-41d4-a716-446655440011' -- Default to AHU General Features
WHERE category_id IS NULL;

-- Step 5: Verification - Show updated mappings
SELECT 'Updated Product Mappings:' as info;
SELECT 
    p.name as product_name, 
    p.category as old_category,
    c.name as new_category_name,
    CASE WHEN c.parent_id IS NOT NULL THEN 
        (SELECT name FROM categories WHERE id = c.parent_id) 
    ELSE 'Main Category' 
    END as parent_category
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
ORDER BY c.parent_id, c.sort_order, p.name;

-- Step 6: Show category hierarchy with product counts
SELECT 'Category Hierarchy with Product Counts:' as info;
SELECT 
    CASE WHEN parent_id IS NULL THEN '📁 ' || name ELSE '  └─ 📄 ' || name END as category_tree,
    slug,
    (SELECT count(*) FROM products WHERE category_id = categories.id) as product_count
FROM categories 
ORDER BY COALESCE(parent_id, id), sort_order;

-- Step 7: Show any products that might still be unmapped
SELECT 'Unmapped Products (should be empty):' as info;
SELECT name, category, category_id 
FROM products 
WHERE category_id IS NULL;

-- Step 8: Summary
SELECT 'Mapping Summary:' as info;
SELECT 
    'Total Products:' as metric, 
    count(*) as count 
FROM products
UNION ALL
SELECT 
    'Mapped Products:' as metric, 
    count(*) as count 
FROM products 
WHERE category_id IS NOT NULL
UNION ALL
SELECT 
    'Unmapped Products:' as metric, 
    count(*) as count 
FROM products 
WHERE category_id IS NULL;
