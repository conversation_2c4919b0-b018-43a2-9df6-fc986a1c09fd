-- Debug Air Handling Units Category Page
-- This script will help us understand why products aren't showing

-- Step 1: Check the Air Handling Units category
SELECT 'Air Handling Units Category Info:' as info;
SELECT id, name, slug, description, parent_id, is_active 
FROM categories 
WHERE slug = 'air-handling-units' OR name ILIKE '%Air Handling%';

-- Step 2: Check all subcategories under Air Handling Units
SELECT 'Air Handling Units Subcategories:' as info;
SELECT id, name, slug, description, parent_id, is_active, sort_order
FROM categories 
WHERE parent_id = '550e8400-e29b-41d4-a716-446655440001'
ORDER BY sort_order;

-- Step 3: Check products directly assigned to main Air Handling Units category
SELECT 'Products in Main Air Handling Units Category:' as info;
SELECT p.id, p.name, p.category, p.category_id, b.name as brand_name
FROM products p
LEFT JOIN brands b ON p.brand_id = b.id
WHERE p.category_id = '550e8400-e29b-41d4-a716-446655440001';

-- Step 4: Check products in ALL Air Handling Units subcategories
SELECT 'Products in Air Handling Units Subcategories:' as info;
SELECT 
    p.id, 
    p.name as product_name, 
    p.category as old_category,
    p.category_id,
    c.name as category_name,
    c.slug as category_slug,
    b.name as brand_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
WHERE p.category_id IN (
    SELECT id FROM categories WHERE parent_id = '550e8400-e29b-41d4-a716-446655440001'
)
ORDER BY c.name, p.name;

-- Step 5: Check what the API query should return for 'air-handling-units' slug
SELECT 'API Query Simulation - Products for air-handling-units slug:' as info;

-- This simulates what happens when someone visits /products?category=air-handling-units
-- First, find the category by slug
SELECT 'Category lookup by slug:' as step;
SELECT id, name, slug FROM categories WHERE slug = 'air-handling-units';

-- Then find products in that category AND its subcategories
SELECT 'Products that should be returned:' as step;
WITH target_category AS (
    SELECT id FROM categories WHERE slug = 'air-handling-units'
),
all_related_categories AS (
    -- Get the main category and all its subcategories
    SELECT id FROM categories WHERE id = (SELECT id FROM target_category)
    UNION
    SELECT id FROM categories WHERE parent_id = (SELECT id FROM target_category)
)
SELECT 
    p.id,
    p.name as product_name,
    p.description,
    p.price,
    p.category_id,
    c.name as category_name,
    c.slug as category_slug,
    b.name as brand_name,
    b.id as brand_id
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
WHERE p.category_id IN (SELECT id FROM all_related_categories)
ORDER BY p.name;

-- Step 6: Check if there are any products with old category strings that might match
SELECT 'Products with old category field matching AHU:' as info;
SELECT id, name, category, category_id 
FROM products 
WHERE category ILIKE '%AHU%' OR category ILIKE '%Air%';

-- Step 7: Show total product count by category for verification
SELECT 'Product Count by Category:' as info;
SELECT 
    c.name as category_name,
    c.slug,
    c.parent_id,
    count(p.id) as product_count
FROM categories c
LEFT JOIN products p ON c.id = p.category_id
GROUP BY c.id, c.name, c.slug, c.parent_id
ORDER BY c.parent_id NULLS FIRST, c.sort_order;

-- Step 8: Check if products have all required fields for display
SELECT 'Product Data Completeness Check:' as info;
SELECT 
    p.name,
    CASE WHEN p.description IS NULL THEN 'Missing Description' ELSE 'Has Description' END as description_status,
    CASE WHEN p.price IS NULL THEN 'Missing Price' ELSE 'Has Price' END as price_status,
    CASE WHEN p.brand_id IS NULL THEN 'Missing Brand' ELSE 'Has Brand' END as brand_status,
    CASE WHEN p.category_id IS NULL THEN 'Missing Category' ELSE 'Has Category' END as category_status
FROM products p
WHERE p.category_id IN (
    SELECT id FROM categories 
    WHERE id = '550e8400-e29b-41d4-a716-446655440001' 
       OR parent_id = '550e8400-e29b-41d4-a716-446655440001'
);
