-- Clean Fix Script - Handles existing data safely
-- This script will work even if categories already exist

-- Step 1: Check current state
SELECT 'Current database state:' as info;
SELECT 'Products:' as table_name, count(*) as count FROM products
UNION ALL
SELECT 'Brands:' as table_name, count(*) as count FROM brands
UNION ALL
SELECT 'Categories:' as table_name, count(*) as count FROM categories;

-- Step 2: Fix products without valid brands
DO $$
DECLARE
    default_brand_id UUID;
    brand_count INTEGER;
BEGIN
    SELECT count(*) INTO brand_count FROM brands;
    
    IF brand_count = 0 THEN
        INSERT INTO brands (name, description, featured)
        VALUES ('Default Brand', 'Default brand for products', false)
        RETURNING id INTO default_brand_id;
        RAISE NOTICE 'Created default brand with ID: %', default_brand_id;
    ELSE
        SELECT id INTO default_brand_id FROM brands LIMIT 1;
        RAISE NOTICE 'Using existing brand with ID: %', default_brand_id;
    END IF;
    
    UPDATE products 
    SET brand_id = default_brand_id 
    WHERE brand_id IS NULL 
       OR brand_id NOT IN (SELECT id FROM brands);
END $$;

-- Step 3: Add category_id column if needed
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID;
        RAISE NOTICE 'Added category_id column to products table';
    END IF;
END $$;

-- Step 4: Clear existing category assignments to avoid foreign key issues
UPDATE products SET category_id = NULL;

-- Step 5: Remove existing categories to start fresh
DELETE FROM categories;

-- Step 6: Create main categories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
('550e8400-e29b-41d4-a716-************', 'Air Handling Units', 'air-handling-units', 'Complete air handling systems', NULL, 1, true),
('550e8400-e29b-41d4-a716-************', 'Heat Recovery Systems', 'heat-recovery-systems', 'Energy efficient heat recovery', NULL, 2, true),
('550e8400-e29b-41d4-a716-************', 'Fan Coil Units', 'fan-coil-units', 'Compact heating and cooling', NULL, 3, true),
('550e8400-e29b-41d4-a716-************', 'Cooling Systems', 'cooling-systems', 'Precision cooling systems', NULL, 4, true),
('550e8400-e29b-41d4-a716-************', 'Condensing Units', 'condensing-units', 'Outdoor condensing units', NULL, 5, true),
('550e8400-e29b-41d4-a716-446655440006', 'Heat Pumps', 'heat-pumps', 'Heat pump systems', NULL, 6, true),
('550e8400-e29b-41d4-a716-446655440007', 'Exhaust Systems', 'exhaust-systems', 'Ventilation and exhaust', NULL, 7, true),
('550e8400-e29b-41d4-a716-446655440008', 'Electrical Enclosures', 'electrical-enclosures', 'Electrical protection', NULL, 8, true);

-- Step 7: Create subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
-- Air Handling Unit subcategories
('650e8400-e29b-41d4-a716-446655440011', 'AHU General Features', 'ahu-general-features', 'Standard air handling units', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440012', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems', '550e8400-e29b-41d4-a716-************', 2, true),
('650e8400-e29b-41d4-a716-446655440013', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Clean room air handlers', '550e8400-e29b-41d4-a716-************', 3, true),
('650e8400-e29b-41d4-a716-446655440014', 'Packaged Hygienic AHU', 'packaged-hygienic-ahu', 'Pre-assembled hygienic systems', '550e8400-e29b-41d4-a716-************', 4, true),
('650e8400-e29b-41d4-a716-446655440015', 'AHU With Heat Recovery', 'ahu-with-heat-recovery', 'Air handlers with heat recovery', '550e8400-e29b-41d4-a716-************', 5, true),
('650e8400-e29b-41d4-a716-446655440016', 'Pool Dehumidification Unit', 'pool-dehumidification-unit', 'Pool environment systems', '550e8400-e29b-41d4-a716-************', 6, true),

-- Heat Recovery subcategories
('650e8400-e29b-41d4-a716-446655440021', 'Heat Recovery Ventilation', 'hrv-systems', 'HRV systems for residential and commercial', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440022', 'Energy Recovery Ventilation', 'erv-systems', 'ERV systems with humidity control', '550e8400-e29b-41d4-a716-************', 2, true),

-- Fan Coil Unit subcategories
('650e8400-e29b-41d4-a716-446655440031', 'Wall Mounted FCU', 'wall-mounted-fcu', 'Wall mounted fan coil units', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440032', 'Ceiling Mounted FCU', 'ceiling-mounted-fcu', 'Ceiling mounted fan coil units', '550e8400-e29b-41d4-a716-************', 2, true),
('650e8400-e29b-41d4-a716-446655440033', 'Floor Standing FCU', 'floor-standing-fcu', 'Floor standing fan coil units', '550e8400-e29b-41d4-a716-************', 3, true),

-- Cooling System subcategories
('650e8400-e29b-41d4-a716-446655440041', 'Precision Air Conditioning', 'precision-air-conditioning', 'High-precision cooling systems', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440042', 'Ecology Units', 'ecology-units', 'Environmental control systems', '550e8400-e29b-41d4-a716-************', 2, true),

-- Heat Pump subcategories (using different slugs to avoid conflicts)
('650e8400-e29b-41d4-a716-446655440051', 'Water Source Heat Pumps', 'water-source-hp', 'Water source heat pump systems', '550e8400-e29b-41d4-a716-446655440006', 1, true),
('650e8400-e29b-41d4-a716-446655440052', 'Air Source Heat Pumps', 'air-source-hp', 'Air source heat pump systems', '550e8400-e29b-41d4-a716-446655440006', 2, true),

-- Exhaust System subcategories
('650e8400-e29b-41d4-a716-446655440061', 'General Exhaust', 'general-exhaust', 'General exhaust systems', '550e8400-e29b-41d4-a716-446655440007', 1, true),
('650e8400-e29b-41d4-a716-446655440062', 'Kitchen Exhaust', 'kitchen-exhaust', 'Kitchen exhaust systems', '550e8400-e29b-41d4-a716-446655440007', 2, true),
('650e8400-e29b-41d4-a716-446655440063', 'Industrial Exhaust', 'industrial-exhaust', 'Industrial exhaust systems', '550e8400-e29b-41d4-a716-446655440007', 3, true);

-- Step 8: Update products to use category_id
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440011' WHERE category = 'AHU';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440021' WHERE category = 'HRV';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440022' WHERE category = 'ERV';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440031' WHERE category = 'FCU';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440042' WHERE category = 'Ecology Unit';
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Condensing Unit';
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440051' WHERE category = 'Water Source Heat Pump';
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-446655440007' WHERE category = 'Exhaust Unit';

-- Step 9: Add foreign key constraint if needed
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'products_category_id_fkey' 
        AND table_name = 'products'
    ) THEN
        ALTER TABLE products ADD CONSTRAINT products_category_id_fkey 
        FOREIGN KEY (category_id) REFERENCES categories(id);
        RAISE NOTICE 'Added foreign key constraint for category_id';
    END IF;
END $$;

-- Step 10: Create indexes
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Step 11: Final verification and results
SELECT 'Setup completed successfully!' as message;

SELECT 'Final Results:' as info;
SELECT 'Categories total:' as metric, count(*) as count FROM categories
UNION ALL
SELECT 'Main categories:' as metric, count(*) as count FROM categories WHERE parent_id IS NULL
UNION ALL
SELECT 'Subcategories:' as metric, count(*) as count FROM categories WHERE parent_id IS NOT NULL
UNION ALL
SELECT 'Products with category_id:' as metric, count(*) as count FROM products WHERE category_id IS NOT NULL
UNION ALL
SELECT 'Products with valid brands:' as metric, count(*) as count FROM products p JOIN brands b ON p.brand_id = b.id;

-- Show category hierarchy
SELECT 'Category Hierarchy:' as info;
SELECT 
    CASE WHEN parent_id IS NULL THEN '📁 ' || name ELSE '  └─ 📄 ' || name END as category_tree,
    slug,
    (SELECT count(*) FROM products WHERE category_id = categories.id) as products
FROM categories 
ORDER BY COALESCE(parent_id, id), sort_order;

-- Show sample products
SELECT 'Sample Products:' as info;
SELECT p.name as product, c.name as category, b.name as brand
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
LIMIT 5;
