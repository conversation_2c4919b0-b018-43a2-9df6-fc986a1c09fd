-- Add featured column to brands table
ALTER TABLE brands ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE;

-- Remove model column from products table (as requested)
ALTER TABLE products DROP COLUMN IF EXISTS model;

-- Add featured column to products table
ALTER TABLE products ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE;

-- Create index for featured brands and products
CREATE INDEX IF NOT EXISTS idx_brands_featured ON brands(featured);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(featured);

-- Update existing brands to set featured status
UPDATE brands SET featured = TRUE WHERE name IN ('ACS Klima', 'HiRef', 'DKC Europe');
