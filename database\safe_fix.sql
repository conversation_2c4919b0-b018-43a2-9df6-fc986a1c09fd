-- Safe Fix Script - Works around foreign key constraints
-- This script fixes the issues without deleting existing data

-- Step 1: Fix products without valid brands first
DO $$
DECLARE
    default_brand_id UUID;
BEGIN
    -- Check if there's a default brand, if not create one
    SELECT id INTO default_brand_id FROM brands WHERE name = 'Default Brand' LIMIT 1;
    
    IF default_brand_id IS NULL THEN
        INSERT INTO brands (id, name, description, featured)
        VALUES (
            '550e8400-e29b-41d4-a716-************',
            'Default Brand',
            'Default brand for products without assigned brands',
            false
        );
        default_brand_id := '550e8400-e29b-41d4-a716-************';
    END IF;
    
    -- Update products without valid brands
    UPDATE products 
    SET brand_id = default_brand_id 
    WHERE brand_id IS NULL 
       OR brand_id NOT IN (SELECT id FROM brands);
END $$;

-- Step 2: Ensure category_id column exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id);
    END IF;
END $$;

-- Step 3: Insert categories only if they don't exist (using ON CONFLICT)
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
('550e8400-e29b-41d4-a716-************', 'Air Handling Units', 'air-handling-units', 'Complete air handling and treatment systems', NULL, 1, true),
('550e8400-e29b-41d4-a716-************', 'Heat Recovery Systems', 'heat-recovery-systems', 'Energy efficient heat recovery systems', NULL, 2, true),
('550e8400-e29b-41d4-a716-************', 'Fan Coil Units', 'fan-coil-units', 'Compact heating and cooling units', NULL, 3, true),
('550e8400-e29b-41d4-a716-************', 'Cooling Systems', 'cooling-systems', 'Precision cooling and air conditioning', NULL, 4, true),
('550e8400-e29b-41d4-a716-************', 'Condensing Units', 'condensing-units', 'Outdoor condensing units', NULL, 5, true),
('550e8400-e29b-41d4-a716-************', 'Heat Pumps', 'heat-pumps', 'Energy efficient heat pump systems', NULL, 6, true),
('550e8400-e29b-41d4-a716-446655440007', 'Exhaust Systems', 'exhaust-systems', 'Ventilation and exhaust systems', NULL, 7, true),
('550e8400-e29b-41d4-a716-446655440008', 'Electrical Enclosures', 'electrical-enclosures', 'Electrical protection solutions', NULL, 8, true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    slug = EXCLUDED.slug,
    description = EXCLUDED.description,
    sort_order = EXCLUDED.sort_order;

-- Step 4: Insert subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
-- Air Handling Unit subcategories
('650e8400-e29b-41d4-a716-446655440011', 'AHU General Features', 'ahu-general-features', 'Standard air handling units', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440012', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems', '550e8400-e29b-41d4-a716-************', 2, true),
('650e8400-e29b-41d4-a716-446655440013', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Clean room air handlers', '550e8400-e29b-41d4-a716-************', 3, true),
('650e8400-e29b-41d4-a716-446655440014', 'Packaged Hygienic AHU', 'packaged-hygienic-ahu', 'Pre-assembled hygienic systems', '550e8400-e29b-41d4-a716-************', 4, true),
('650e8400-e29b-41d4-a716-446655440015', 'AHU With Heat Recovery', 'ahu-with-heat-recovery', 'Air handlers with heat recovery', '550e8400-e29b-41d4-a716-************', 5, true),
('650e8400-e29b-41d4-a716-446655440016', 'Pool Dehumidification Unit', 'pool-dehumidification-unit', 'Pool environment systems', '550e8400-e29b-41d4-a716-************', 6, true),

-- Heat Recovery subcategories
('650e8400-e29b-41d4-a716-446655440021', 'Heat Recovery Ventilation', 'heat-recovery-ventilation', 'HRV systems', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440022', 'Energy Recovery Ventilation', 'energy-recovery-ventilation', 'ERV systems', '550e8400-e29b-41d4-a716-************', 2, true),

-- Fan Coil Unit subcategories
('650e8400-e29b-41d4-a716-446655440031', 'Wall Mounted FCU', 'wall-mounted-fcu', 'Wall mounted fan coil units', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440032', 'Ceiling Mounted FCU', 'ceiling-mounted-fcu', 'Ceiling mounted fan coil units', '550e8400-e29b-41d4-a716-************', 2, true),
('650e8400-e29b-41d4-a716-446655440033', 'Floor Standing FCU', 'floor-standing-fcu', 'Floor standing fan coil units', '550e8400-e29b-41d4-a716-************', 3, true),

-- Cooling System subcategories
('650e8400-e29b-41d4-a716-446655440041', 'Precision Air Conditioning', 'precision-air-conditioning', 'High-precision cooling systems', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440042', 'Ecology Units', 'ecology-units', 'Environmental control systems', '550e8400-e29b-41d4-a716-************', 2, true),

-- Heat Pump subcategories
('650e8400-e29b-41d4-a716-446655440051', 'Water Source Heat Pumps', 'water-source-heat-pumps', 'Water source heat pumps', '550e8400-e29b-41d4-a716-************', 1, true),
('650e8400-e29b-41d4-a716-446655440052', 'Air Source Heat Pumps', 'air-source-heat-pumps', 'Air source heat pumps', '550e8400-e29b-41d4-a716-************', 2, true),

-- Exhaust System subcategories
('650e8400-e29b-41d4-a716-446655440061', 'General Exhaust', 'general-exhaust', 'General exhaust systems', '550e8400-e29b-41d4-a716-446655440007', 1, true),
('650e8400-e29b-41d4-a716-446655440062', 'Kitchen Exhaust', 'kitchen-exhaust', 'Kitchen exhaust systems', '550e8400-e29b-41d4-a716-446655440007', 2, true),
('650e8400-e29b-41d4-a716-446655440063', 'Industrial Exhaust', 'industrial-exhaust', 'Industrial exhaust systems', '550e8400-e29b-41d4-a716-446655440007', 3, true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    slug = EXCLUDED.slug,
    description = EXCLUDED.description,
    parent_id = EXCLUDED.parent_id,
    sort_order = EXCLUDED.sort_order;

-- Step 5: Update existing products to use category_id (only if they don't have one)
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440011' WHERE category = 'AHU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440021' WHERE category = 'HRV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440022' WHERE category = 'ERV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440031' WHERE category = 'FCU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440042' WHERE category = 'Ecology Unit' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Condensing Unit' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440051' WHERE category = 'Water Source Heat Pump' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-446655440007' WHERE category = 'Exhaust Unit' AND category_id IS NULL;

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Step 7: Verification
SELECT 'Safe setup completed! Results:' as message;
SELECT 'Categories total:' as info, count(*) as count FROM categories;
SELECT 'Main categories:' as info, count(*) as count FROM categories WHERE parent_id IS NULL;
SELECT 'Subcategories:' as info, count(*) as count FROM categories WHERE parent_id IS NOT NULL;
SELECT 'Products with category_id:' as info, count(*) as count FROM products WHERE category_id IS NOT NULL;
SELECT 'Products without category_id:' as info, count(*) as count FROM products WHERE category_id IS NULL;
SELECT 'Products with valid brands:' as info, count(*) as count FROM products p JOIN brands b ON p.brand_id = b.id;

-- Show the category hierarchy
SELECT 'Category Hierarchy:' as info;
SELECT 
    CASE WHEN parent_id IS NULL THEN name ELSE '  └─ ' || name END as category_tree,
    slug,
    (SELECT count(*) FROM products WHERE category_id = categories.id) as product_count
FROM categories 
ORDER BY 
    COALESCE(parent_id, id), 
    sort_order;
