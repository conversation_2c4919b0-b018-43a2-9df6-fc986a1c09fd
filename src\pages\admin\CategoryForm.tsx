/**
 * Enhanced Category Management Form
 * Admin interface for managing hierarchical product categories
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { Plus, Edit, Trash2, Save, X, ChevronRight, ChevronDown, Folder, FolderOpen, Package, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { cmsService } from '@/services/cmsService';
import type { CategoryTreeNode } from '@/services/categoryService';

interface CategoryFormData {
  name: string;
  description: string;
  slug: string;
  parent_id: string | null;
  sort_order: number;
  is_active: boolean;
}

const CategoryForm = () => {
  const [categoryTree, setCategoryTree] = useState<CategoryTreeNode[]>([]);
  const [flatCategories, setFlatCategories] = useState<CategoryTreeNode[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<CategoryFormData>({
    defaultValues: {
      name: '',
      description: '',
      slug: '',
      parent_id: null,
      sort_order: 0,
      is_active: true
    }
  });

  const watchName = watch('name');

  // Move all useEffect hooks after the loadCategories definition

  // Auto-generate slug from name
  useEffect(() => {
    if (watchName && !editingId) {
      const slug = watchName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setValue('slug', slug);
    }
  }, [watchName, editingId, setValue]);

  const flattenTree = useCallback((nodes: CategoryTreeNode[], level = 0): CategoryTreeNode[] => {
    const result: CategoryTreeNode[] = [];

    nodes.forEach(node => {
      result.push({ ...node, sort_order: level }); // Use sort_order to track level for display
      if (node.children && node.children.length > 0) {
        result.push(...flattenTree(node.children, level + 1));
      }
    });

    return result;
  }, []);

  const loadCategories = useCallback(async () => {
    try {
      setIsLoading(true);
      const tree = await cmsService.getCategoryTree();
      console.log('Loaded category tree:', tree); // Debug log

      // Filter out any invalid categories
      const validTree = tree.filter(cat => cat && cat.id && cat.id.trim() !== '');
      console.log('Valid category tree:', validTree); // Debug log
      setCategoryTree(validTree);

      // Flatten tree for parent selection dropdown
      const flattened = flattenTree(validTree);
      // Additional filtering to ensure no empty IDs
      const validFlattened = flattened.filter(cat => cat && cat.id && cat.id.trim() !== '');
      setFlatCategories(validFlattened);

      // Expand main categories by default
      const mainCategoryIds = validTree.map(cat => cat.id).filter(id => id && id.trim() !== '');
      setExpandedNodes(new Set(mainCategoryIds));

    } catch (error) {
      console.error('Error loading categories:', error);
      setMessage({ type: 'error', text: 'Failed to load categories. Please ensure the database migration has been applied.' });

      // Set empty arrays as fallback
      setCategoryTree([]);
      setFlatCategories([]);
      setExpandedNodes(new Set());
    } finally {
      setIsLoading(false);
    }
  }, [flattenTree]);

  // Add useEffect hooks after loadCategories is defined
  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  // Add a visibility change listener to refresh data when user returns to the page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh the category tree to get updated product counts
        loadCategories();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Also refresh when the component mounts or when focus returns to window
    const handleFocus = () => {
      loadCategories();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [loadCategories]);

  // Add periodic refresh to keep product counts up to date
  useEffect(() => {
    const interval = setInterval(() => {
      // Only refresh if the page is visible to avoid unnecessary API calls
      if (!document.hidden) {
        loadCategories();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [loadCategories]);

  const onSubmit = async (data: CategoryFormData) => {
    try {
      if (editingId) {
        // Update existing category
        await cmsService.updateCategory(editingId, {
          name: data.name.trim(),
          slug: data.slug.trim(),
          description: data.description.trim() || null,
          parent_id: data.parent_id || null,
          sort_order: data.sort_order,
          is_active: data.is_active
        });
        setMessage({ type: 'success', text: 'Category updated successfully' });
      } else {
        // Create new category
        await cmsService.createCategory({
          name: data.name.trim(),
          slug: data.slug.trim(),
          description: data.description.trim() || null,
          parent_id: data.parent_id || null,
          sort_order: data.sort_order,
          is_active: data.is_active
        });
        setMessage({ type: 'success', text: 'Category created successfully' });
      }

      reset();
      setEditingId(null);
      await loadCategories(); // Reload to show changes
    } catch (error) {
      console.error('Error saving category:', error);
      setMessage({ type: 'error', text: 'Failed to save category' });
    }
  };

  const handleEdit = (category: CategoryTreeNode) => {
    setEditingId(category.id);
    setValue('name', category.name);
    setValue('description', category.description || '');
    setValue('slug', category.slug);
    setValue('parent_id', category.parent_id);
    setValue('sort_order', category.sort_order);
    setValue('is_active', category.is_active);
  };

  const handleDelete = async (id: string, hasChildren: boolean) => {
    const confirmMessage = hasChildren
      ? 'Are you sure you want to delete this category and all its subcategories? This action cannot be undone.'
      : 'Are you sure you want to delete this category? This action cannot be undone.';

    if (!confirm(confirmMessage)) return;

    try {
      await cmsService.deleteCategory(id);
      setMessage({ type: 'success', text: 'Category deleted successfully' });
      await loadCategories(); // Reload to show changes
    } catch (error) {
      console.error('Error deleting category:', error);
      setMessage({ type: 'error', text: 'Failed to delete category' });
    }
  };

  const handleCancel = () => {
    reset();
    setEditingId(null);
  };

  const toggleExpanded = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  const renderCategoryTree = (nodes: CategoryTreeNode[], level = 0) => {
    return nodes.map((node) => (
      <div key={node.id} className={`${level > 0 ? 'border-l-2 border-slate-200' : ''}`}>
        <div
          className={`group flex items-center justify-between p-3 rounded-lg transition-all duration-200 ${editingId === node.id
            ? 'bg-blue-50 border border-blue-200 shadow-sm'
            : 'hover:bg-slate-50 hover:shadow-sm'
            }`}
          style={{ marginLeft: `${level * 20}px` }}
        >
          <div className="flex items-center space-x-3 flex-1">
            {node.children.length > 0 ? (
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-6 w-6 hover:bg-slate-200"
                onClick={() => toggleExpanded(node.id)}
              >
                {expandedNodes.has(node.id) ? (
                  <ChevronDown className="h-4 w-4 text-slate-600" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-slate-600" />
                )}
              </Button>
            ) : (
              <div className="w-6" />
            )}

            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${node.children.length > 0
              ? 'bg-blue-100 text-blue-600'
              : 'bg-slate-100 text-slate-600'
              }`}>
              {node.children.length > 0 ? (
                expandedNodes.has(node.id) ? (
                  <FolderOpen className="h-4 w-4" />
                ) : (
                  <Folder className="h-4 w-4" />
                )
              ) : (
                <Package className="h-4 w-4" />
              )}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-slate-800 truncate">{node.name}</span>
                {!node.is_active && (
                  <Badge variant="secondary" className="text-xs">Inactive</Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  {node.product_count} products
                </Badge>
              </div>
              {node.description && (
                <p className="text-sm text-slate-500 mt-1 truncate">{node.description}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(node)}
              className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600"
              title="Edit category"
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(node.id, node.children.length > 0)}
              className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600"
              title="Delete category"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {expandedNodes.has(node.id) && node.children.length > 0 && (
          <div className="ml-4">
            {renderCategoryTree(node.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Enhanced Header Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent mb-3">
                Category Management
              </h1>
              <p className="text-slate-600 text-lg max-w-2xl">
                Create and organize your product catalog with hierarchical categories. Build a structured navigation system for better user experience.
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-white/90 backdrop-blur-sm rounded-xl px-6 py-4 shadow-lg border border-white/20">
                <div className="text-sm text-slate-500 font-medium">Total Categories</div>
                <div className="text-3xl font-bold text-blue-600">{flatCategories.length}</div>
              </div>
              <Button
                onClick={() => loadCategories()}
                variant="outline"
                size="lg"
                className="bg-white/90 backdrop-blur-sm border-white/20 hover:bg-white/95"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {message && (
          <Alert className={`mb-6 ${message.type === 'error'
            ? 'border-red-200 bg-red-50 text-red-800'
            : 'border-green-200 bg-green-50 text-green-800'
            }`}>
            <AlertDescription className="font-medium">{message.text}</AlertDescription>
          </Alert>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Compact Category Form */}
          <div className="xl:col-span-1">
            <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4">
                <CardTitle className="flex items-center space-x-3 text-lg">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    {editingId ? (
                      <Edit className="w-4 h-4" />
                    ) : (
                      <Plus className="w-4 h-4" />
                    )}
                  </div>
                  <span className="text-lg font-semibold">
                    {editingId ? 'Edit Category' : 'Add New Category'}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name" className="text-sm font-semibold text-gray-700 mb-2 block">
                        Category Name *
                      </Label>
                      <Input
                        id="name"
                        {...register('name', { required: 'Category name is required' })}
                        placeholder="Enter category name"
                        className="border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 h-11"
                      />
                      {errors.name && (
                        <p className="text-sm text-red-600 mt-1 flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {errors.name.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="slug" className="text-sm font-semibold text-gray-700 mb-2 block">
                        URL Slug *
                      </Label>
                      <Input
                        id="slug"
                        {...register('slug', { required: 'Slug is required' })}
                        placeholder="category-url-slug"
                        className="border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 h-11"
                      />
                      {errors.slug && (
                        <p className="text-sm text-red-600 mt-1 flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {errors.slug.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="parent_id">Parent Category</Label>
                      <Select
                        value={watch('parent_id') || 'none'}
                        onValueChange={(value) => setValue('parent_id', value === 'none' ? null : value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent category (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No Parent (Main Category)</SelectItem>
                          {flatCategories && flatCategories.length > 0 && flatCategories
                            .filter(cat => cat && cat.id && cat.id.trim() !== '' && cat.id !== editingId) // Don't allow self as parent and ensure valid ID
                            .map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {'  '.repeat(category.sort_order || 0)}{category.name || 'Unnamed Category'}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="sort_order">Sort Order</Label>
                      <Input
                        id="sort_order"
                        type="number"
                        {...register('sort_order', { valueAsNumber: true })}
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      {...register('description')}
                      placeholder="Brief description of the category"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="is_active"
                      checked={watch('is_active')}
                      onCheckedChange={(checked) => setValue('is_active', !!checked)}
                    />
                    <Label htmlFor="is_active">Active Category</Label>
                  </div>

                  <div className="flex gap-2">
                    <Button type="submit">
                      <Save className="w-4 h-4 mr-2" />
                      {editingId ? 'Update' : 'Create'} Category
                    </Button>
                    {editingId && (
                      <Button type="button" variant="outline" onClick={handleCancel}>
                        <X className="w-4 h-4 mr-2" />
                        Cancel
                      </Button>
                    )}
                  </div>
                </form>
              </CardContent>
            </Card>

          </div>

          {/* Enhanced Category Tree - Takes up more space */}
          <div className="xl:col-span-3">
            <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-slate-600 to-slate-700 text-white p-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-3 text-lg">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <Folder className="w-4 h-4" />
                    </div>
                    <span>Category Hierarchy</span>
                  </CardTitle>
                  <div className="flex items-center space-x-4">
                    <div className="text-sm text-white/80">
                      {flatCategories.length} categories • {categoryTree.length} root categories
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {categoryTree.length === 0 ? (
                  <div className="text-center py-16 px-6">
                    <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Package className="h-8 w-8 text-slate-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-slate-700 mb-2">No Categories Yet</h3>
                    <p className="text-slate-500 mb-6 max-w-sm mx-auto">
                      Start building your product catalog by creating your first category using the form on the left.
                    </p>
                    <Button
                      onClick={() => document.getElementById('name')?.focus()}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create First Category
                    </Button>
                  </div>
                ) : (
                  <div className="max-h-[600px] overflow-y-auto">
                    <div className="p-4 space-y-1">
                      {renderCategoryTree(categoryTree)}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Categories Tree */}
          <div className="xl:col-span-2">
            <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-t-lg">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7l-7 7-7-7m14 18l-7-7-7 7" />
                      </svg>
                    </div>
                    <span className="text-lg font-semibold">Category Hierarchy</span>
                  </CardTitle>
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={loadCategories}
                      className="bg-white/20 hover:bg-white/30 text-white"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      <span className="text-xs">Refresh</span>
                    </Button>
                    <div className="bg-white/20 rounded-lg px-3 py-1">
                      <span className="text-sm font-medium">{flatCategories.length} categories</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                {categoryTree.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Package className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
                    <p className="text-gray-500 mb-6">Create your first category to get started organizing your products.</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {renderCategoryTree(categoryTree)}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryForm;
