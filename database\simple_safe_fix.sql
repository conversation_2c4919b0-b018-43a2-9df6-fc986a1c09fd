-- Simple Safe Fix - Minimal changes to get everything working
-- This script makes the minimum changes needed to fix the issues

-- Step 1: Check what we're working with
SELECT 'Current state check:' as info;
SELECT 'Products count:' as info, count(*) as count FROM products;
SELECT 'Brands count:' as info, count(*) as count FROM brands;
SELECT 'Categories count:' as info, count(*) as count FROM categories;

-- Step 2: Show brands table structure
SELECT 'Brands table columns:' as info;
SELECT column_name FROM information_schema.columns WHERE table_name = 'brands';

-- Step 3: Fix products without valid brands (using existing brands)
DO $$
DECLARE
    default_brand_id UUID;
    brand_count INTEGER;
BEGIN
    -- Count existing brands
    SELECT count(*) INTO brand_count FROM brands;
    
    IF brand_count = 0 THEN
        -- No brands exist, create a default one with only the columns that exist
        INSERT INTO brands (name, description, featured)
        VALUES ('Default Brand', 'Default brand for products', false)
        RETURNING id INTO default_brand_id;
    ELSE
        -- Use the first existing brand as default
        SELECT id INTO default_brand_id FROM brands LIMIT 1;
    END IF;
    
    -- Update products without valid brands
    UPDATE products 
    SET brand_id = default_brand_id 
    WHERE brand_id IS NULL 
       OR brand_id NOT IN (SELECT id FROM brands);
       
    RAISE NOTICE 'Updated products to use brand_id: %', default_brand_id;
END $$;

-- Step 4: Add category_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID;
        RAISE NOTICE 'Added category_id column to products table';
    END IF;
END $$;

-- Step 5: Create main categories (only if they don't exist)
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active)
SELECT v.id::uuid, v.name, v.slug, v.description, v.parent_id::uuid, v.sort_order, v.is_active
FROM (VALUES
    ('550e8400-e29b-41d4-a716-************', 'Air Handling Units', 'air-handling-units', 'Complete air handling systems', NULL, 1, true),
    ('550e8400-e29b-41d4-a716-************', 'Heat Recovery Systems', 'heat-recovery-systems', 'Energy efficient heat recovery', NULL, 2, true),
    ('550e8400-e29b-41d4-a716-************', 'Fan Coil Units', 'fan-coil-units', 'Compact heating and cooling', NULL, 3, true),
    ('550e8400-e29b-41d4-a716-************', 'Cooling Systems', 'cooling-systems', 'Precision cooling systems', NULL, 4, true),
    ('550e8400-e29b-41d4-a716-************', 'Condensing Units', 'condensing-units', 'Outdoor condensing units', NULL, 5, true),
    ('550e8400-e29b-41d4-a716-************', 'Heat Pumps', 'heat-pumps', 'Heat pump systems', NULL, 6, true),
    ('550e8400-e29b-41d4-a716-************', 'Exhaust Systems', 'exhaust-systems', 'Ventilation and exhaust', NULL, 7, true),
    ('550e8400-e29b-41d4-a716-************', 'Electrical Enclosures', 'electrical-enclosures', 'Electrical protection', NULL, 8, true)
) AS v(id, name, slug, description, parent_id, sort_order, is_active)
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE categories.id = v.id::uuid);

-- Step 6: Create subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active)
SELECT v.id::uuid, v.name, v.slug, v.description, v.parent_id::uuid, v.sort_order, v.is_active
FROM (VALUES
    -- Air Handling Unit subcategories
    ('650e8400-e29b-41d4-a716-************', 'AHU General Features', 'ahu-general-features', 'Standard air handling units', '550e8400-e29b-41d4-a716-************', 1, true),
    ('650e8400-e29b-41d4-a716-************', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems', '550e8400-e29b-41d4-a716-************', 2, true),
    ('650e8400-e29b-41d4-a716-************', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Clean room air handlers', '550e8400-e29b-41d4-a716-************', 3, true),

    -- Heat Recovery subcategories
    ('650e8400-e29b-41d4-a716-************', 'Heat Recovery Ventilation', 'heat-recovery-ventilation', 'HRV systems', '550e8400-e29b-41d4-a716-************', 1, true),
    ('650e8400-e29b-41d4-a716-************', 'Energy Recovery Ventilation', 'energy-recovery-ventilation', 'ERV systems', '550e8400-e29b-41d4-a716-************', 2, true),

    -- Fan Coil Unit subcategories
    ('650e8400-e29b-41d4-a716-************', 'Wall Mounted FCU', 'wall-mounted-fcu', 'Wall mounted units', '550e8400-e29b-41d4-a716-************', 1, true),
    ('650e8400-e29b-41d4-a716-446655440032', 'Ceiling Mounted FCU', 'ceiling-mounted-fcu', 'Ceiling mounted units', '550e8400-e29b-41d4-a716-************', 2, true),

    -- Cooling System subcategories
    ('650e8400-e29b-41d4-a716-446655440041', 'Precision Air Conditioning', 'precision-air-conditioning', 'High-precision cooling', '550e8400-e29b-41d4-a716-************', 1, true),
    ('650e8400-e29b-41d4-a716-************', 'Ecology Units', 'ecology-units', 'Environmental control systems', '550e8400-e29b-41d4-a716-************', 2, true),

    -- Heat Pump subcategories
    ('650e8400-e29b-41d4-a716-************', 'Water Source Heat Pumps', 'water-source-heat-pumps', 'Water source heat pumps', '550e8400-e29b-41d4-a716-************', 1, true),
    ('650e8400-e29b-41d4-a716-************', 'Air Source Heat Pumps', 'air-source-heat-pumps', 'Air source heat pumps', '550e8400-e29b-41d4-a716-************', 2, true)
) AS v(id, name, slug, description, parent_id, sort_order, is_active)
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE categories.id = v.id::uuid);

-- Step 7: Update products to use category_id
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'AHU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'HRV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'ERV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'FCU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'Ecology Unit' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Condensing Unit' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-************' WHERE category = 'Water Source Heat Pump' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Exhaust Unit' AND category_id IS NULL;

-- Step 8: Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'products_category_id_fkey' 
        AND table_name = 'products'
    ) THEN
        ALTER TABLE products ADD CONSTRAINT products_category_id_fkey 
        FOREIGN KEY (category_id) REFERENCES categories(id);
        RAISE NOTICE 'Added foreign key constraint for category_id';
    END IF;
END $$;

-- Step 9: Create indexes
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Step 10: Final verification
SELECT 'Setup completed successfully!' as message;
SELECT 'Categories created:' as info, count(*) as count FROM categories;
SELECT 'Main categories:' as info, count(*) as count FROM categories WHERE parent_id IS NULL;
SELECT 'Subcategories:' as info, count(*) as count FROM categories WHERE parent_id IS NOT NULL;
SELECT 'Products with category_id:' as info, count(*) as count FROM products WHERE category_id IS NOT NULL;
SELECT 'Products with valid brands:' as info, count(*) as count FROM products p JOIN brands b ON p.brand_id = b.id;

-- Show sample data
SELECT 'Sample products with categories:' as info;
SELECT p.name as product_name, c.name as category_name, b.name as brand_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
LIMIT 5;
