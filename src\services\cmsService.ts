import { supabase } from '../lib/supabase'
import type { Database } from '../lib/supabase'
import { categoryService } from './categoryService'

type Brand = Database['public']['Tables']['brands']['Row']
type Product = Database['public']['Tables']['products']['Row']
type Page = Database['public']['Tables']['pages']['Row']
type Setting = Database['public']['Tables']['settings']['Row']
type Category = Database['public']['Tables']['categories']['Row']

export class CMSService {
  // Brand operations
  async getBrands(): Promise<Brand[]> {
    const { data, error } = await supabase
      .from('brands')
      .select('*')
      .order('name')

    if (error) throw error
    return data || []
  }

  async getBrand(id: string): Promise<Brand | null> {
    const { data, error } = await supabase
      .from('brands')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return data
  }

  async createBrand(brand: Database['public']['Tables']['brands']['Insert']): Promise<Brand> {
    const { data, error } = await supabase
      .from('brands')
      .insert(brand)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateBrand(id: string, updates: Database['public']['Tables']['brands']['Update']): Promise<Brand> {
    const { data, error } = await supabase
      .from('brands')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deleteBrand(id: string): Promise<void> {
    const { error } = await supabase
      .from('brands')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Product operations
  async getProducts(filters?: {
    brandId?: string
    category?: string
    limit?: number
    offset?: number
    search?: string
  }): Promise<{ data: Product[]; count: number }> {
    console.log('CMS Service - getProducts called with filters:', filters) // Debug log

    let query = supabase
      .from('products')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })

    if (filters?.brandId) {
      query = query.eq('brand_id', filters.brandId)
    }

    if (filters?.category) {
      // First try to find category by slug (new system)
      try {
        const { data: categoryData, error: categoryError } = await supabase
          .from('categories')
          .select('id')
          .eq('slug', filters.category)
          .single()

        if (categoryData && !categoryError) {
          // Get all related category IDs (main category + subcategories)
          const { data: subcategories } = await supabase
            .from('categories')
            .select('id')
            .eq('parent_id', categoryData.id)

          const allCategoryIds = [categoryData.id]
          if (subcategories) {
            allCategoryIds.push(...subcategories.map(sub => sub.id))
          }

          // Use new category_id system with subcategories included
          query = query.in('category_id', allCategoryIds)
        } else {
          // Fallback to old category string system
          query = query.eq('category', filters.category)
        }
      } catch (error) {
        // If categories table doesn't exist or has issues, fallback to old system
        console.log('CMS Service - Falling back to old category system:', error)
        query = query.eq('category', filters.category)
      }
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
    }

    if (filters?.limit) {
      query = query.limit(filters.limit)
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
    }

    const { data, error, count } = await query

    if (error) {
      console.error('CMS Service - Error fetching products:', error) // Debug log
      throw error
    }

    console.log('CMS Service - Products fetched successfully:', { count, dataLength: data?.length }) // Debug log
    return { data: data || [], count: count || 0 }
  }

  async getProduct(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return data
  }

  async getProductBySlug(slug: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return data
  }

  async createProduct(product: Database['public']['Tables']['products']['Insert']): Promise<Product> {
    console.log('Creating product with data:', product) // Debug log

    const { data, error } = await supabase
      .from('products')
      .insert(product)
      .select()
      .single()

    if (error) {
      console.error('Database error creating product:', error) // Debug log
      throw error
    }

    console.log('Product created successfully:', data) // Debug log
    return data
  }

  async updateProduct(id: string, updates: Database['public']['Tables']['products']['Update']): Promise<Product> {
    console.log('Updating product with data:', updates) // Debug log

    const { data, error } = await supabase
      .from('products')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Database error updating product:', error) // Debug log
      throw error
    }

    console.log('Product updated successfully:', data) // Debug log
    return data
  }

  async deleteProduct(id: string): Promise<void> {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Page operations
  async getPages(): Promise<Page[]> {
    const { data, error } = await supabase
      .from('pages')
      .select('*')
      .eq('published', true)
      .order('title')

    if (error) throw error
    return data || []
  }

  async getPage(slug: string): Promise<Page | null> {
    const { data, error } = await supabase
      .from('pages')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return data
  }

  async createPage(page: Database['public']['Tables']['pages']['Insert']): Promise<Page> {
    const { data, error } = await supabase
      .from('pages')
      .insert(page)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updatePage(id: string, updates: Database['public']['Tables']['pages']['Update']): Promise<Page> {
    const { data, error } = await supabase
      .from('pages')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deletePage(id: string): Promise<void> {
    const { error } = await supabase
      .from('pages')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Settings operations
  async getSettings(): Promise<Setting[]> {
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .order('key')

    if (error) throw error
    return data || []
  }

  async getSetting(key: string): Promise<Setting | null> {
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .eq('key', key)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return data
  }

  async setSetting(key: string, value: any, description?: string): Promise<Setting> {
    const { data, error } = await supabase
      .from('settings')
      .upsert({
        key,
        value,
        description,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deleteSetting(key: string): Promise<void> {
    const { error } = await supabase
      .from('settings')
      .delete()
      .eq('key', key)

    if (error) throw error
  }

  // Category operations (delegated to categoryService)
  async getCategories() {
    return await categoryService.getCategories()
  }

  async getAllCategories() {
    return await categoryService.getAllCategories()
  }

  async getCategory(id: string) {
    return await categoryService.getCategory(id)
  }

  async getCategoryBySlug(slug: string) {
    return await categoryService.getCategoryBySlug(slug)
  }

  async getMainCategories() {
    return await categoryService.getMainCategories()
  }

  async getSubcategories(parentId: string) {
    return await categoryService.getSubcategories(parentId)
  }

  async createCategory(category: Database['public']['Tables']['categories']['Insert']) {
    return await categoryService.createCategory(category)
  }

  async updateCategory(id: string, updates: Database['public']['Tables']['categories']['Update']) {
    return await categoryService.updateCategory(id, updates)
  }

  async deleteCategory(id: string) {
    return await categoryService.deleteCategory(id)
  }

  async getCategoryTree() {
    return await categoryService.getCategoryTree()
  }

  async reorderCategories(categoryOrders: { id: string; sort_order: number }[]) {
    return await categoryService.reorderCategories(categoryOrders)
  }
}

export const cmsService = new CMSService()
