-- Fix Featured Brands
-- This script ensures all brands are marked as featured so they show up in "Our Partner Brands"

-- First, let's see what we have
SELECT id, name, featured FROM brands;

-- Update all existing brands to be featured
UPDATE brands SET featured = true;

-- Verify the update
SELECT id, name, featured FROM brands;

-- Alternative: Update specific brands only
-- UPDATE brands SET featured = true WHERE name IN ('ACS Klima', 'HiRef', 'DKC Europe');

-- Alternative: Update by specific IDs (from your console log)
-- UPDATE brands SET featured = true WHERE id IN ('2638019a-12c4-4e22-8598-6d1b8c8cd90f', 'bb354d8d-2599-4eb0-ba05-8f97f694dad5');

-- Ensure the featured column exists and has proper default
-- ALTER TABLE brands ALTER COLUMN featured SET DEFAULT true;

-- For future brands, they will default to featured=true in the form
