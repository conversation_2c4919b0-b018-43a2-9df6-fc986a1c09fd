import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Star, Zap, Shield, Award, ExternalLink, Building2, Package, AlertCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { useBrands, useProductsByBrand } from '@/hooks/useProducts';
import { cmsService } from '@/services/cmsService';
import { useState, useEffect } from 'react';

import { ModernProductCard } from '@/components/product';

// Brand Card Component with Product Titles
const BrandCard = ({ brand, productCount }: { brand: any; productCount: number }) => {
  const { data: brandProducts = [], isLoading: productsLoading, error: productsError } = useProductsByBrand(brand.id, 5);

  // Show first 3-5 product titles
  const displayProducts = brandProducts.slice(0, 4);
  const remainingCount = Math.max(0, productCount - displayProducts.length);

  return (
    <Card className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02] group">
      <CardContent className="p-6 sm:p-8 text-center min-h-[420px] sm:min-h-[450px] flex flex-col">
        {/* Logo */}
        <div className="w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 bg-white rounded-lg flex items-center justify-center shadow-sm">
          <img
            src={brand.logo}
            alt={`${brand.name} Logo`}
            className="max-w-16 max-h-16 sm:max-w-20 sm:max-h-20 object-contain"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder.svg';
            }}
          />
        </div>

        {/* Brand Info */}
        <h3 className="text-lg sm:text-xl font-bold text-foreground mb-2">{brand.name}</h3>
        <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4 line-clamp-2">
          {brand.description}
        </p>

        {/* Brand Details */}
        <div className="flex items-center justify-center gap-3 sm:gap-4 text-xs text-muted-foreground mb-3 sm:mb-4">
          <span className="flex items-center">
            <Building2 className="h-3 w-3 mr-1" />
            {brand.country}
          </span>
          <span>{productCount} Products</span>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mb-4 sm:mb-6">
          <Link
            to={`/products?brand=${brand.id}`}
            className="flex-1"
            onClick={() => {
              // Scroll to top when navigating
              setTimeout(() => window.scrollTo({ top: 0, behavior: 'smooth' }), 100);
            }}
          >
            <Button
              variant="outline"
              size="sm"
              className="w-full"
            >
              View Products ({productCount})
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(brand.website, '_blank')}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>

        {/* Product Titles Section */}
        <div className="flex-1 flex flex-col justify-start">
          {productsLoading ? (
            <div className="flex items-center justify-center text-xs text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
              Loading products...
            </div>
          ) : productsError ? (
            <div className="flex items-center justify-center text-xs text-muted-foreground">
              <AlertCircle className="h-3 w-3 mr-1" />
              Unable to load products
            </div>
          ) : displayProducts.length > 0 ? (
            <div className="text-left">
              <div className="flex items-center mb-2 sm:mb-3">
                <Package className="h-3 w-3 mr-1 text-primary" />
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Featured Products
                </span>
              </div>
              <ul className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-foreground">
                {displayProducts.map((product, index) => (
                  <li key={product.id} className="flex items-start">
                    <span className="inline-block w-1 h-1 bg-primary rounded-full mt-1.5 sm:mt-2 mr-2 flex-shrink-0"></span>
                    <span className="line-clamp-1 hover:text-primary transition-colors cursor-default text-left">
                      {product.name}
                    </span>
                  </li>
                ))}
                {remainingCount > 0 && (
                  <li className="text-xs text-muted-foreground italic">
                    + {remainingCount} more product{remainingCount !== 1 ? 's' : ''}...
                  </li>
                )}
              </ul>
            </div>
          ) : productCount > 0 ? (
            <div className="flex items-center justify-center text-xs text-muted-foreground">
              <Package className="h-3 w-3 mr-1" />
              {productCount} product{productCount !== 1 ? 's' : ''} available
            </div>
          ) : null}
        </div>
      </CardContent>
    </Card>
  );
};

const Products = () => {
  // Fetch brands from database
  const { data: dbBrands = [], isLoading } = useBrands();
  const [brandProductCounts, setBrandProductCounts] = useState<Record<string, number>>({});

  // Use database brands
  const brands = dbBrands;

  // Filter featured brands
  const featuredBrands = brands.filter(brand => brand.featured);

  // Fetch product counts for each brand
  useEffect(() => {
    const fetchProductCounts = async () => {
      const counts: Record<string, number> = {};

      for (const brand of featuredBrands) {
        try {
          const result = await cmsService.getProducts({ brandId: brand.id, limit: 1 });
          counts[brand.id] = result.count;
        } catch (error) {
          console.error(`Error fetching product count for brand ${brand.id}:`, error);
          counts[brand.id] = 0;
        }
      }

      setBrandProductCounts(counts);
    };

    if (featuredBrands.length > 0) {
      fetchProductCounts();
    }
  }, [featuredBrands]);

  // Show loading state while fetching brands
  if (isLoading) {
    return (
      <section id="products" className="py-20 bg-gradient-to-br from-background via-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-96 mx-auto mb-8"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-64 bg-gray-300 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // If no featured brands found, don't render the section
  if (featuredBrands.length === 0) {
    return null;
  }

  return (
    <section id="products" className="py-20 bg-gradient-to-br from-background via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-32 left-20 w-64 h-64 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-32 right-20 w-80 h-80 bg-accent rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full mb-6">
            <Star className="h-4 w-4 text-primary mr-2" />
            <span className="text-primary font-semibold text-sm uppercase tracking-wider">Premium MEP Solutions</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent leading-tight">
            Our Partner Brands
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            We partner with world-leading manufacturers to bring you the highest quality HVAC and MEP equipment.
            Discover premium brands trusted by professionals worldwide.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-16">
          {featuredBrands.map((brand) => (
            <BrandCard
              key={brand.id}
              brand={brand}
              productCount={brandProductCounts[brand.id] ?? 0}
            />
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-foreground">
              Ready to Explore Our Complete Product Range?
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Browse products by brand, discover detailed specifications, and find the perfect MEP solution for your project
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link to="/products">
                <Button size="lg" className="w-full sm:w-auto bg-gradient-primary hover:opacity-90 shadow-lg min-h-[44px] px-8">
                  Browse All Products <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Product Features Section */}
        <div className="bg-gradient-to-r from-primary/10 via-accent/5 to-primary/10 rounded-2xl p-8 mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              Why Choose Our Partner Brands?
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Every brand is carefully selected for their proven track record of excellence and innovation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Quality Assured</h4>
              <p className="text-muted-foreground">
                All products meet international standards with comprehensive quality testing
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Energy Efficient</h4>
              <p className="text-muted-foreground">
                Advanced technology ensures maximum efficiency and reduced operational costs
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Award Winning</h4>
              <p className="text-muted-foreground">
                Industry-recognized products with proven performance and reliability
              </p>
            </div>
          </div>
        </div>



      </div>
    </section>
  );
};

export default Products;