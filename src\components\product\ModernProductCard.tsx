/**
 * Modern Product Card Component
 * Displays product information with modern design and Supabase integration
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Star, Eye, Heart } from 'lucide-react';
import { ProductWithBrand } from '@/services/productService';

interface ModernProductCardProps {
  product: ProductWithBrand;
  showCategory?: boolean;
  showBrand?: boolean;
  showSpecs?: boolean;
  maxSpecs?: number;
  className?: string;
  onImageError?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  onFavorite?: (productId: string) => void;
  isFavorite?: boolean;
}



const ModernProductCard = ({
  product,
  showCategory = true,
  showBrand = true,
  showSpecs = true,
  maxSpecs = 3,
  className = "",
  onImageError,
  onFavorite,
  isFavorite = false
}: ModernProductCardProps) => {
  const mainImage = product.images?.[0];

  // Get key specifications for display
  const keySpecs = product.specifications ?
    Object.entries(product.specifications).slice(0, maxSpecs) : [];

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.src = '/placeholder.svg';
    onImageError?.(e);
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavorite?.(product.id);
  };

  return (
    <div className={`group hover:-translate-y-1 transition-all duration-300 ${className}`}>
      <Card className="h-full bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-0">
        <Link to={`/products/${product.slug}`} className="block">
          {/* Product Image with Status Badge */}
          <div className="relative aspect-[4/3] overflow-hidden bg-gray-50">
            {mainImage ? (
              <img
                src={mainImage}
                alt={product.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <div className="text-gray-400 text-center">
                  <Eye className="w-8 h-8 mx-auto mb-2" />
                  <span className="text-sm">No Image</span>
                </div>
              </div>
            )}



            {/* Favorite Button */}
            {onFavorite && (
              <button
                onClick={handleFavoriteClick}
                className="absolute bottom-3 right-3 p-2 bg-white bg-opacity-90 rounded-full shadow-md hover:bg-opacity-100 transition-all duration-200"
              >
                <Heart
                  className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`}
                />
              </button>
            )}

            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300" />
          </div>

          {/* Product Information */}
          <CardContent className="p-5">
            {/* Brand and Category */}
            {(showBrand || showCategory) && (
              <div className="flex items-center justify-between mb-2">
                {showBrand && product.brand && (
                  <span className="text-sm text-blue-600 font-medium">
                    {product.brand.name}
                  </span>
                )}
                {showCategory && (
                  <span className="text-xs text-gray-500 capitalize">
                    {product.category}
                  </span>
                )}
              </div>
            )}

            {/* Product Title */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {product.name}
            </h3>

            {/* Short Description */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {product.description.length > 100
                ? product.description.substring(0, 100) + '...'
                : product.description}
            </p>

            {/* Key Specifications */}
            {showSpecs && keySpecs.length > 0 && (
              <div className="mb-4 space-y-1">
                {keySpecs.map(([key, value], index) => (
                  <div key={index} className="flex justify-between text-xs text-gray-600">
                    <span className="font-medium">{key}:</span>
                    <span className="text-right">{value}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Action */}
            <div className="flex justify-end mt-4">
              <Button
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors group"
              >
                View Details
                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </CardContent>
        </Link>
      </Card>
    </div>
  );
};

export default ModernProductCard;
