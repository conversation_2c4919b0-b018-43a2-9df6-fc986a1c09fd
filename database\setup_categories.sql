-- Quick setup script to fix the "No products found" and "No subcategories" issues
-- Run this script in your Supabase SQL editor to set up the category system

-- Step 1: Create categories table if it doesn't exist (from migration 005)
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Add category_id column to products table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id);
    END IF;
END $$;

-- Step 3: Insert categories using proper UUIDs
-- First, let's create variables for the UUIDs to ensure consistency
DO $$
DECLARE
    ahu_id UUID := '550e8400-e29b-41d4-a716-************';
    hrv_id UUID := '550e8400-e29b-41d4-a716-************';
    fcu_id UUID := '550e8400-e29b-41d4-a716-************';
    cooling_id UUID := '550e8400-e29b-41d4-a716-************';
    condensing_id UUID := '550e8400-e29b-41d4-a716-************';
    heatpump_id UUID := '550e8400-e29b-41d4-a716-************';
    exhaust_id UUID := '550e8400-e29b-41d4-a716-************';
    electrical_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Insert main categories
    INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
    (ahu_id, 'Air Handling Units', 'air-handling-units', 'Complete air handling and treatment systems for commercial and industrial applications', NULL, 1, true),
    (hrv_id, 'Heat Recovery Systems', 'heat-recovery-systems', 'Energy efficient heat recovery and ventilation systems', NULL, 2, true),
    (fcu_id, 'Fan Coil Units', 'fan-coil-units', 'Compact heating and cooling units for individual room control', NULL, 3, true),
    (cooling_id, 'Cooling Systems', 'cooling-systems', 'Precision cooling and air conditioning systems', NULL, 4, true),
    (condensing_id, 'Condensing Units', 'condensing-units', 'Outdoor condensing units for refrigeration and air conditioning', NULL, 5, true),
    (heatpump_id, 'Heat Pumps', 'heat-pumps', 'Energy efficient heating and cooling heat pump systems', NULL, 6, true),
    (exhaust_id, 'Exhaust Systems', 'exhaust-systems', 'Ventilation and exhaust systems for air quality management', NULL, 7, true),
    (electrical_id, 'Electrical Enclosures', 'electrical-enclosures', 'Protection and housing solutions for electrical equipment', NULL, 8, true)
    ON CONFLICT (id) DO NOTHING;

    -- Insert subcategories for Air Handling Units
    INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
    ('650e8400-e29b-41d4-a716-446655440011', 'AHU General Features', 'ahu-general-features', 'Standard air handling units with basic features', ahu_id, 1, true),
    ('650e8400-e29b-41d4-a716-446655440012', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems', ahu_id, 2, true),
    ('650e8400-e29b-41d4-a716-446655440013', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Clean room and pharmaceutical grade air handlers', ahu_id, 3, true),
    ('650e8400-e29b-41d4-a716-446655440014', 'Packaged Hygienic AHU', 'packaged-hygienic-ahu', 'Pre-assembled hygienic air handling systems', ahu_id, 4, true),
    ('650e8400-e29b-41d4-a716-446655440015', 'AHU With Heat Recovery', 'ahu-with-heat-recovery', 'Air handlers with integrated heat recovery', ahu_id, 5, true),
    ('650e8400-e29b-41d4-a716-446655440016', 'Pool Dehumidification Unit', 'pool-dehumidification-unit', 'Specialized units for pool and spa environments', ahu_id, 6, true)
    ON CONFLICT (id) DO NOTHING;

    -- Insert subcategories for Heat Recovery Systems
    INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
    ('650e8400-e29b-41d4-a716-446655440021', 'Heat Recovery Ventilation', 'heat-recovery-ventilation', 'HRV systems for residential and commercial use', hrv_id, 1, true),
    ('650e8400-e29b-41d4-a716-446655440022', 'Energy Recovery Ventilation', 'energy-recovery-ventilation', 'ERV systems with humidity control', hrv_id, 2, true)
    ON CONFLICT (id) DO NOTHING;

    -- Insert subcategories for Cooling Systems
    INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
    ('650e8400-e29b-41d4-a716-446655440031', 'Precision Air Conditioning', 'precision-air-conditioning', 'High-precision cooling for data centers and critical environments', cooling_id, 1, true),
    ('650e8400-e29b-41d4-a716-446655440032', 'Ecology Units', 'ecology-units', 'Environmental control systems for specialized applications', cooling_id, 2, true)
    ON CONFLICT (id) DO NOTHING;

    -- Insert subcategories for Heat Pumps
    INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) VALUES
    ('650e8400-e29b-41d4-a716-446655440041', 'Water Source Heat Pumps', 'water-source-heat-pumps', 'Efficient heat pumps using water as heat source', heatpump_id, 1, true),
    ('650e8400-e29b-41d4-a716-446655440042', 'Air Source Heat Pumps', 'air-source-heat-pumps', 'Heat pumps using ambient air as heat source', heatpump_id, 2, true)
    ON CONFLICT (id) DO NOTHING;
END $$;

-- Step 4: Update products to use category_id based on their current category string
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440011' WHERE category = 'AHU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440021' WHERE category = 'HRV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440022' WHERE category = 'ERV' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'FCU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440032' WHERE category = 'Ecology Unit' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Condensing Unit' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440041' WHERE category = 'Water Source Heat Pump' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-************' WHERE category = 'Exhaust Unit' AND category_id IS NULL;

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Verification queries (optional - run these to check the setup)
-- SELECT 'Categories created:' as info, count(*) as count FROM categories;
-- SELECT 'Products with category_id:' as info, count(*) as count FROM products WHERE category_id IS NOT NULL;
-- SELECT 'Products without category_id:' as info, count(*) as count FROM products WHERE category_id IS NULL;
