-- Debug script to check what's in your database
-- Run this first to see what data exists

-- Check if tables exist
SELECT 'Tables check:' as info;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('products', 'categories', 'brands');

-- Check products table structure
SELECT 'Products table columns:' as info;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'products';

-- Check categories table structure (if exists)
SELECT 'Categories table columns:' as info;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'categories';

-- Count records
SELECT 'Record counts:' as info;
SELECT 'products' as table_name, count(*) as count FROM products
UNION ALL
SELECT 'categories' as table_name, count(*) as count FROM categories
UNION ALL
SELECT 'brands' as table_name, count(*) as count FROM brands;

-- Show all products
SELECT 'All products:' as info;
SELECT id, name, category, category_id, brand_id FROM products;

-- Show all categories (if any)
SELECT 'All categories:' as info;
SELECT id, name, slug, parent_id, is_active FROM categories ORDER BY sort_order;

-- Show all brands
SELECT 'All brands:' as info;
SELECT id, name, featured FROM brands;

-- Check for any products without brands
SELECT 'Products without valid brands:' as info;
SELECT p.id, p.name, p.brand_id 
FROM products p 
LEFT JOIN brands b ON p.brand_id = b.id 
WHERE b.id IS NULL;
