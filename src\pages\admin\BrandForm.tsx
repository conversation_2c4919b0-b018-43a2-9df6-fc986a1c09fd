import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import ImageUpload from '@/components/ui/ImageUpload'
import { cmsService } from '@/services/cmsService'
import { ArrowLeft, Save } from 'lucide-react'

interface BrandFormData {
  name: string
  logo: string
  description: string
  website: string
  country: string
  established: number | null
  featured: boolean
}

export default function BrandForm() {
  const { id } = useParams()
  const navigate = useNavigate()
  const isEditing = Boolean(id)

  const [formData, setFormData] = useState<BrandFormData>({
    name: '',
    logo: '',
    description: '',
    website: '',
    country: '',
    established: null,
    featured: true // Default to featured for new brands
  })

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (isEditing && id) {
      loadBrand(id)
    }
  }, [id, isEditing])

  const loadBrand = async (brandId: string) => {
    try {
      setLoading(true)
      const brand = await cmsService.getBrand(brandId)
      if (brand) {
        setFormData({
          name: brand.name,
          logo: brand.logo || '',
          description: brand.description || '',
          website: brand.website || '',
          country: brand.country || '',
          established: brand.established,
          featured: brand.featured || false
        })
      }
    } catch (error) {
      console.error('Error loading brand:', error)
      alert('Error loading brand data')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof BrandFormData, value: string | number | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) {
      alert('Brand name is required')
      return
    }

    try {
      setSaving(true)

      const brandData = {
        name: formData.name.trim(),
        logo: formData.logo.trim() || null,
        description: formData.description.trim() || null,
        website: formData.website.trim() || null,
        country: formData.country.trim() || null,
        established: formData.established,
        featured: formData.featured
      }

      if (isEditing && id) {
        await cmsService.updateBrand(id, brandData)
      } else {
        await cmsService.createBrand(brandData)
      }

      navigate('/admin')
    } catch (error) {
      console.error('Error saving brand:', error)
      alert('Error saving brand. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading brand data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="flex items-center space-x-4 mb-8">
        <Button variant="outline" onClick={() => navigate('/admin')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Brand' : 'Add New Brand'}
          </h1>
          <p className="text-gray-600 mt-2">
            {isEditing ? 'Update brand information' : 'Create a new HVAC brand'}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Brand Information</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Brand Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter brand name"
                required
              />
            </div>

            <ImageUpload
              label="Brand Logo"
              value={formData.logo}
              onChange={(value) => handleInputChange('logo', value)}
              placeholder="Upload brand logo or enter URL"
              accept="image/*"
              maxSize={5}
            />

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of the brand"
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://www.brandwebsite.com"
                type="url"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  placeholder="USA, Japan, Germany, etc."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="established">Established Year</Label>
                <Input
                  id="established"
                  type="number"
                  value={formData.established || ''}
                  onChange={(e) => handleInputChange('established', e.target.value ? parseInt(e.target.value) : null)}
                  placeholder="1950"
                  min="1800"
                  max={new Date().getFullYear()}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2 pt-4">
              <Switch
                id="featured"
                checked={formData.featured}
                onCheckedChange={(checked) => handleInputChange('featured', checked)}
              />
              <Label htmlFor="featured" className="text-sm font-medium">
                Featured Brand
              </Label>
              <p className="text-xs text-gray-500 ml-2">
                (Featured brands appear in "Our Partner Brands" section on homepage)
              </p>
            </div>

            <div className="flex justify-end space-x-4 pt-6">
              <Button type="button" variant="outline" onClick={() => navigate('/admin')}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : (isEditing ? 'Update Brand' : 'Create Brand')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
