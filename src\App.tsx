import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Corporate from "./pages/Corporate";
import MissionVision from "./pages/MissionVision";
import ModernProductsPage from "./pages/ModernProductsPage";
import BrandPage from "./pages/BrandPage";
import ModernProductDetail from "./pages/ModernProductDetail";
import Solutions from "./pages/Solutions";
import References from "./pages/References";
import ContactPage from "./pages/ContactPage";

import AdminDashboard from "./pages/admin/Dashboard";
import BrandForm from "./pages/admin/BrandForm";
import ProductForm from "./pages/admin/ProductForm";
import CategoryForm from "./pages/admin/CategoryForm";
import Settings from "./pages/admin/Settings";
import Login from "./pages/admin/Login";
import AdminSetup from "./pages/admin/AdminSetup";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { AuthProvider } from "./contexts/AuthContext";
import NotFound from "./pages/NotFound";
import WhatsAppFloat from "./components/WhatsAppFloat";
import { initPerformanceMonitoring } from "./utils/performance";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
});

// Initialize performance monitoring
if (typeof window !== 'undefined') {
  initPerformanceMonitoring();
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/corporate" element={<Corporate />} />
            <Route path="/mission-vision" element={<MissionVision />} />
            <Route path="/products" element={<ModernProductsPage />} />
            <Route path="/brands/:brandId" element={<BrandPage />} />
            <Route path="/products/:slug" element={<ModernProductDetail />} />
            <Route path="/solutions" element={<Solutions />} />
            <Route path="/solutions/:category" element={<Solutions />} />
            <Route path="/references" element={<References />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/admin/setup" element={<AdminSetup />} />
            <Route path="/admin/login" element={<Login />} />
            <Route path="/admin" element={<ProtectedRoute><AdminDashboard /></ProtectedRoute>} />
            <Route path="/admin/brands/new" element={<ProtectedRoute><BrandForm /></ProtectedRoute>} />
            <Route path="/admin/brands/:id" element={<ProtectedRoute><BrandForm /></ProtectedRoute>} />
            <Route path="/admin/products/new" element={<ProtectedRoute><ProductForm /></ProtectedRoute>} />
            <Route path="/admin/products/:id" element={<ProtectedRoute><ProductForm /></ProtectedRoute>} />
            <Route path="/admin/categories" element={<ProtectedRoute><CategoryForm /></ProtectedRoute>} />
            <Route path="/admin/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
          <WhatsAppFloat />
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
