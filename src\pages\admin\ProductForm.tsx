import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import ImageUpload from '@/components/ui/ImageUpload'
import { cmsService } from '@/services/cmsService'
import { ArrowLeft, Save, Plus, X } from 'lucide-react'

interface ProductFormData {
  brand_id: string
  name: string
  category: string
  description: string
  specifications: Record<string, string>
  features: string[]
  images: string[]
  slug: string
}

// Categories will be loaded dynamically from the database

export default function ProductForm() {
  const { id } = useParams()
  const navigate = useNavigate()
  const isEditing = Boolean(id)

  const [brands, setBrands] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [formData, setFormData] = useState<ProductFormData>({
    brand_id: '',
    name: '',
    category: '',
    description: '',
    specifications: {},
    features: [],
    images: [],
    slug: ''
  })

  const [newSpecKey, setNewSpecKey] = useState('')
  const [newSpecValue, setNewSpecValue] = useState('')
  const [newFeature, setNewFeature] = useState('')
  const [newImage, setNewImage] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadBrands()
    loadCategories()
    if (isEditing && id) {
      loadProduct(id)
    }
  }, [id, isEditing])

  const loadBrands = async () => {
    try {
      const brandsData = await cmsService.getBrands()
      setBrands(brandsData)
    } catch (error) {
      console.error('Error loading brands:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const categoriesData = await cmsService.getAllCategories()
      setCategories(categoriesData)
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const loadProduct = async (productId: string) => {
    try {
      setLoading(true)
      const product = await cmsService.getProduct(productId)
      if (product) {
        console.log('Loaded product data:', product) // Debug log
        console.log('Product images:', product.images) // Debug log

        setFormData({
          brand_id: product.brand_id,
          name: product.name,
          category: product.category,
          description: product.description || '',
          specifications: product.specifications || {},
          features: Array.isArray(product.features) ? product.features : [],
          images: Array.isArray(product.images) ? product.images : [],
          slug: product.slug
        })
      }
    } catch (error) {
      console.error('Error loading product:', error)
      alert('Error loading product data')
    } finally {
      setLoading(false)
    }
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value }

      // Auto-generate slug when name changes
      if (field === 'name') {
        updated.slug = generateSlug(value)
      }

      return updated
    })
  }

  const addSpecification = () => {
    if (newSpecKey.trim() && newSpecValue.trim()) {
      setFormData(prev => ({
        ...prev,
        specifications: {
          ...prev.specifications,
          [newSpecKey.trim()]: newSpecValue.trim()
        }
      }))
      setNewSpecKey('')
      setNewSpecValue('')
    }
  }

  const removeSpecification = (key: string) => {
    setFormData(prev => {
      const { [key]: removed, ...rest } = prev.specifications
      return { ...prev, specifications: rest }
    })
  }

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }))
      setNewFeature('')
    }
  }

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  const addImage = () => {
    const imageUrl = newImage.trim()
    console.log('Adding image URL:', imageUrl) // Debug log
    if (imageUrl) {
      // Basic URL validation
      try {
        new URL(imageUrl)
        setFormData(prev => {
          const newImages = [...prev.images, imageUrl]
          console.log('Updated images array:', newImages) // Debug log
          return {
            ...prev,
            images: newImages
          }
        })
        setNewImage('')
      } catch (error) {
        // If not a valid URL, still add it (might be a relative path)
        setFormData(prev => {
          const newImages = [...prev.images, imageUrl]
          console.log('Updated images array (non-URL):', newImages) // Debug log
          return {
            ...prev,
            images: newImages
          }
        })
        setNewImage('')
      }
    }
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || !formData.brand_id || !formData.category) {
      alert('Please fill in all required fields')
      return
    }

    try {
      setSaving(true)

      const productData = {
        brand_id: formData.brand_id,
        name: formData.name.trim(),
        category: formData.category,
        description: formData.description.trim(),
        slug: formData.slug.trim(),
        specifications: formData.specifications || {},
        features: Array.isArray(formData.features) ? formData.features : [],
        images: Array.isArray(formData.images) ? formData.images : [], // Ensure it's an array
        featured: false // Default value for featured
      }

      console.log('Submitting product data:', productData) // Debug log

      if (isEditing && id) {
        await cmsService.updateProduct(id, productData)
      } else {
        await cmsService.createProduct(productData)
      }

      navigate('/admin')
    } catch (error) {
      console.error('Error saving product:', error)
      console.error('Product data that failed:', productData) // Debug log
      alert('Error saving product. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading product data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex items-center space-x-4 mb-8">
        <Button variant="outline" onClick={() => navigate('/admin')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Product' : 'Add New Product'}
          </h1>
          <p className="text-gray-600 mt-2">
            {isEditing ? 'Update product information' : 'Create a new HVAC product'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="brand">Brand *</Label>
                <Select value={formData.brand_id} onValueChange={(value) => handleInputChange('brand_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a brand" />
                  </SelectTrigger>
                  <SelectContent>
                    {brands.map((brand) => (
                      <SelectItem key={brand.id} value={brand.id}>
                        {brand.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name}>
                        {'  '.repeat(category.parent_id ? 1 : 0)}{category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter product name"
                  required
                />
              </div>

            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">URL Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => handleInputChange('slug', e.target.value)}
                placeholder="product-url-slug"
              />
              <p className="text-sm text-gray-500">Auto-generated from name</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Product description"
                rows={4}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Specifications */}
        <Card>
          <CardHeader>
            <CardTitle>Specifications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              <Input
                placeholder="Specification name"
                value={newSpecKey}
                onChange={(e) => setNewSpecKey(e.target.value)}
              />
              <Input
                placeholder="Specification value"
                value={newSpecValue}
                onChange={(e) => setNewSpecValue(e.target.value)}
              />
              <Button type="button" onClick={addSpecification}>
                <Plus className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>

            <div className="space-y-2">
              {Object.entries(formData.specifications).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span><strong>{key}:</strong> {value}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeSpecification(key)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <Card>
          <CardHeader>
            <CardTitle>Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder="Add a feature"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                className="flex-1"
              />
              <Button type="button" onClick={addFeature}>
                <Plus className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>

            <div className="space-y-2">
              {formData.features.map((feature, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span>{feature}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeFeature(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Product Images
              <Badge variant="secondary">{formData.images.length} image{formData.images.length !== 1 ? 's' : ''}</Badge>
            </CardTitle>
            <CardDescription>
              Upload images or enter URLs. Uploaded images are automatically added to the product gallery.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ImageUpload
              label="Add Product Image"
              value={newImage}
              onChange={(value) => {
                console.log('ImageUpload onChange called with:', value) // Debug log
                setNewImage(value)
                // Auto-add uploaded images to the array (for file uploads, not manual URLs)
                if (value && value.trim() && (value.startsWith('data:') || value.startsWith('http'))) {
                  console.log('Auto-adding image to array:', value) // Debug log
                  setFormData(prev => {
                    const newImages = [...prev.images, value.trim()]
                    console.log('Updated images array:', newImages) // Debug log
                    return {
                      ...prev,
                      images: newImages
                    }
                  })
                  setNewImage('') // Clear the input after adding
                }
              }}
              placeholder="Upload product image or enter URL"
              accept="image/*"
              maxSize={10}
            />

            <div className="flex items-center space-x-2">
              <Input
                placeholder="Or enter image URL manually"
                value={newImage}
                onChange={(e) => setNewImage(e.target.value)}
                className="flex-1"
              />
              <Button type="button" onClick={addImage} disabled={!newImage.trim()}>
                <Plus className="h-4 w-4 mr-2" />
                Add URL
              </Button>
            </div>

            {formData.images.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative">
                    <img
                      src={image}
                      alt={`Product image ${index + 1}`}
                      className="w-full h-32 object-cover rounded border"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder.svg'
                      }}
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => removeImage(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>



        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => navigate('/admin')}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : (isEditing ? 'Update Product' : 'Create Product')}
          </Button>
        </div>
      </form>
    </div>
  )
}
