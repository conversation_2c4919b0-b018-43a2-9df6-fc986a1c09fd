import { Mail, Phone, MapPin, Linkedin, Facebook, Twitter, ArrowRight, Clock, Award, Shield, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const Footer = () => {
  const services = [
    { name: "Electrical Systems", href: "/services#electrical" },
    { name: "Plumbing Systems", href: "/services#plumbing" },
    { name: "HVAC Systems", href: "/services#hvac" },
    { name: "Fire Protection", href: "/services#fire-protection" },
    { name: "Building Automation", href: "/services#automation" },
    { name: "Maintenance Services", href: "/services#maintenance" }
  ];

  const quickLinks = [
    { name: "About Us", href: "/corporate" },
    { name: "Our Products", href: "/products" },
    { name: "Solutions", href: "/solutions" },
    { name: "References", href: "/references" },
    { name: "Contact", href: "/contact" },
    { name: "Careers", href: "/careers" }
  ];



  return (
    <footer className="bg-gradient-hero text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-accent rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        {/* Newsletter Section */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-16 border border-white/20">
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Stay Updated with Nile Pro</h3>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Get the latest updates on MEP innovations, project insights, and industry trends delivered to your inbox.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-primary"
            />
            <Button className="bg-gradient-primary hover:opacity-90 px-8">
              Subscribe
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <img
                src="/logo.png"
                alt="Nile Pro Logo"
                className="h-12 w-auto"
              />
              <div>
                <div className="text-2xl font-bold">Nile Pro</div>
                <div className="text-sm opacity-90">MEP Construction Excellence</div>
              </div>
            </div>

            <p className="text-gray-300 mb-6 leading-relaxed">
              Engineering excellence in MEP solutions since 2009. We deliver comprehensive mechanical,
              electrical, and plumbing services for construction projects of all scales with guaranteed quality and satisfaction.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">9+</div>
                <div className="text-xs text-gray-400">Years</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent">50+</div>
                <div className="text-xs text-gray-400">Projects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">100%</div>
                <div className="text-xs text-gray-400">Satisfaction</div>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              <a href="#" className="group w-12 h-12 bg-white/10 rounded-full flex items-center justify-center hover:bg-primary hover:scale-110 transition-all duration-300" aria-label="LinkedIn">
                <Linkedin className="h-5 w-5 group-hover:animate-bounce" />
              </a>
              <a href="#" className="group w-12 h-12 bg-white/10 rounded-full flex items-center justify-center hover:bg-primary hover:scale-110 transition-all duration-300" aria-label="Facebook">
                <Facebook className="h-5 w-5 group-hover:animate-bounce" />
              </a>
              <a href="#" className="group w-12 h-12 bg-white/10 rounded-full flex items-center justify-center hover:bg-primary hover:scale-110 transition-all duration-300" aria-label="Twitter">
                <Twitter className="h-5 w-5 group-hover:animate-bounce" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-6 flex items-center">
              <Award className="h-5 w-5 text-primary mr-2" />
              Our Services
            </h3>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <a href={service.href} className="text-gray-300 hover:text-primary transition-colors flex items-center group">
                    <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {service.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6 flex items-center">
              <CheckCircle className="h-5 w-5 text-primary mr-2" />
              Quick Links
            </h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a href={link.href} className="text-gray-300 hover:text-primary transition-colors flex items-center group">
                    <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6 flex items-center">
              <Phone className="h-5 w-5 text-primary mr-2" />
              Contact Info
            </h3>
            <div className="space-y-6">
              <div className="flex items-start space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                <MapPin className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <div className="text-gray-300">
                  <div className="font-medium">Office Location</div>
                  <div className="text-sm">El-Nozha, Cairo</div>
                  <div className="text-sm">Egypt</div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                <Phone className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <div className="text-gray-300">
                  <div className="font-medium">Phone Numbers</div>
                  <div className="text-sm">+201281008799</div>
                  <div className="text-sm">+20 ************</div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                <Mail className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <div className="text-gray-300">
                  <div className="font-medium">Email Addresses</div>
                  <div className="text-sm"><EMAIL></div>
                  <div className="text-sm"><EMAIL></div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                <Clock className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <div className="text-gray-300">
                  <div className="font-medium">Working Hours</div>
                  <div className="text-sm">Mon - Fri: 8:00 AM - 6:00 PM</div>
                  <div className="text-sm">Sat: 9:00 AM - 4:00 PM</div>
                </div>
              </div>
            </div>


          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 mt-16 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
              <div className="text-gray-300 text-sm">
                © 2025 Nile Pro for Construction. All rights reserved.
              </div>

            </div>

            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <a href="/privacy" className="text-gray-300 hover:text-primary transition-colors flex items-center group">
                <ArrowRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                Privacy Policy
              </a>
              <a href="/terms" className="text-gray-300 hover:text-primary transition-colors flex items-center group">
                <ArrowRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                Terms of Service
              </a>
              <a href="/cookies" className="text-gray-300 hover:text-primary transition-colors flex items-center group">
                <ArrowRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                Cookie Policy
              </a>
              <a href="/sitemap" className="text-gray-300 hover:text-primary transition-colors flex items-center group">
                <ArrowRight className="h-3 w-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                Sitemap
              </a>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-6 pt-6 border-t border-white/10 text-center space-y-4">
            <p className="text-xs text-gray-400 max-w-4xl mx-auto">
              Nile Pro MEP is a leading construction company specializing in mechanical, electrical, and plumbing systems.
              We serve commercial, industrial, and residential projects across Egypt with a commitment to quality, safety, and innovation.
            </p>
            <div className="flex items-center justify-center space-x-2 text-xs">
              <span className="text-gray-500">Crafted with</span>
              <span className="text-red-500 animate-pulse text-sm">❤️</span>
              <span className="text-gray-500">by</span>
              <a
                href="https://www.codesafir.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative inline-flex items-center space-x-1 text-primary hover:text-accent transition-all duration-300 font-semibold"
              >
                <span className="relative">
                  CodeSafir
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent group-hover:w-full transition-all duration-300"></span>
                </span>
                <svg
                  className="w-3 h-3 transform group-hover:translate-x-1 group-hover:-translate-y-0.5 transition-transform duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;