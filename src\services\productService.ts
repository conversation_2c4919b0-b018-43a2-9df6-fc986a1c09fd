/**
 * Product Service for Supabase CMS integration
 * Handles all product-related API operations
 */

import { cmsService } from './cmsService'
import type { Database } from '../lib/supabase'

type Product = Database['public']['Tables']['products']['Row']
type Brand = Database['public']['Tables']['brands']['Row']

export interface ProductWithBrand extends Product {
  brand?: Brand
}

export interface ProductQueryParams {
  category?: string
  brandId?: string
  limit?: number
  offset?: number
  search?: string
}

class ProductService {
  /**
   * Get products with filtering, sorting, and pagination
   */
  async getProducts(params: ProductQueryParams = {}): Promise<{
    data: ProductWithBrand[];
    count: number;
  }> {
    console.log('Product Service - getProducts called with params:', params) // Debug log

    const filters: any = {}

    if (params.category) {
      filters.category = params.category
    }
    if (params.brandId) {
      filters.brandId = params.brandId
    }
    if (params.limit) {
      filters.limit = params.limit
    }
    if (params.offset) {
      filters.offset = params.offset
    }
    if (params.search) {
      filters.search = params.search
    }

    console.log('Product Service - Calling CMS service with filters:', filters) // Debug log
    const result = await cmsService.getProducts(filters)

    console.log('Product Service - Got result from CMS:', { count: result.count, dataLength: result.data.length }) // Debug log

    // Get brand information for each product
    const productsWithBrands = await Promise.all(
      result.data.map(async (product) => {
        try {
          const brand = await cmsService.getBrand(product.brand_id)
          return { ...product, brand }
        } catch (error) {
          console.error('Product Service - Error fetching brand for product:', product.id, error) // Debug log
          return { ...product, brand: null }
        }
      })
    )

    console.log('Product Service - Final result:', { count: result.count, productsWithBrandsLength: productsWithBrands.length }) // Debug log

    return {
      data: productsWithBrands,
      count: result.count
    }
  }

  /**
   * Get a single product by slug
   */
  async getProductBySlug(slug: string): Promise<ProductWithBrand | null> {
    const product = await cmsService.getProductBySlug(slug)
    if (!product) return null

    const brand = await cmsService.getBrand(product.brand_id)
    return { ...product, brand }
  }

  /**
   * Get a single product by ID
   */
  async getProductById(id: string): Promise<ProductWithBrand | null> {
    const product = await cmsService.getProduct(id)
    if (!product) return null

    const brand = await cmsService.getBrand(product.brand_id)
    return { ...product, brand }
  }



  /**
   * Get products by brand
   */
  async getProductsByBrand(brandId: string, limit?: number): Promise<ProductWithBrand[]> {
    const result = await this.getProducts({
      brandId,
      limit
    })
    return result.data
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(category: string, limit?: number): Promise<ProductWithBrand[]> {
    const result = await this.getProducts({
      category,
      limit
    })
    return result.data
  }

  /**
   * Search products
   */
  async searchProducts(query: string, limit?: number): Promise<ProductWithBrand[]> {
    // For now, we'll do a simple search on the frontend
    // In a real implementation, you'd want to add full-text search to Supabase
    const result = await this.getProducts({ limit: limit || 50 })

    const searchTerm = query.toLowerCase()
    return result.data.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.category.toLowerCase().includes(searchTerm) ||
      product.brand?.name.toLowerCase().includes(searchTerm)
    )
  }

  /**
   * Get product categories (flat list for filtering)
   */
  async getCategories() {
    return await cmsService.getAllCategories()
  }

  /**
   * Get hierarchical categories (for navigation)
   */
  async getHierarchicalCategories() {
    return await cmsService.getCategories()
  }

  /**
   * Get main categories only
   */
  async getMainCategories() {
    return await cmsService.getMainCategories()
  }

  /**
   * Get category by slug
   */
  async getCategoryBySlug(slug: string) {
    return await cmsService.getCategoryBySlug(slug)
  }

  /**
   * Get all brands
   */
  async getBrands(): Promise<Brand[]> {
    return await cmsService.getBrands()
  }
}

export const productService = new ProductService()
