-- Fix array default values for proper PostgreSQL array handling
-- The current defaults '{}' should be proper empty arrays

-- Fix features array default
ALTER TABLE products ALTER COLUMN features SET DEFAULT ARRAY[]::TEXT[];

-- Fix images array default  
ALTER TABLE products ALTER COLUMN images SET DEFAULT ARRAY[]::TEXT[];

-- Update any existing NULL values to empty arrays
UPDATE products SET features = ARRAY[]::TEXT[] WHERE features IS NULL;
UPDATE products SET images = ARRAY[]::TEXT[] WHERE images IS NULL;

-- Ensure the columns are not null
ALTER TABLE products ALTER COLUMN features SET NOT NULL;
ALTER TABLE products ALTER COLUMN images SET NOT NULL;
