export interface ProductSpecification {
  name: string;
  value: string;
  unit?: string;
}

export interface ProductFeature {
  title: string;
  description: string;
  icon?: string;
}

export interface ProductApplication {
  sector: string;
  description: string;
  benefits: string[];
}

export interface ProductImage {
  url: string;
  alt: string;
  caption?: string;
  isMain?: boolean;
}

export interface ProductDownload {
  title: string;
  type: 'datasheet' | 'manual' | 'brochure' | 'certificate';
  url: string;
  fileSize?: string;
  format: 'PDF' | 'DOC' | 'XLS';
}

export interface Product {
  id: string;
  title: string;
  slug: string;
  category: ProductCategory;
  shortDescription: string;
  fullDescription: string;
  features: ProductFeature[];
  specifications: ProductSpecification[];
  applications: ProductApplication[];
  images: ProductImage[];
  downloads: ProductDownload[];
  tags: string[];
  brand?: string;
  brandId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type ProductCategory =
  | 'air-handling'
  | 'cooling'
  | 'ventilation'
  | 'heating'
  | 'heat-pumps'
  | 'exhaust'
  | 'fan-coil'
  | 'electrical'
  | 'air-filtration';

export interface ProductCategoryInfo {
  id: ProductCategory;
  name: string;
  description: string;
  icon: string;
}
