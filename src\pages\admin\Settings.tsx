import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { cmsService } from '@/services/cmsService'
import { ArrowLeft, Save, Plus, Edit, Trash2 } from 'lucide-react'

interface Setting {
  id: string
  key: string
  value: any
  description: string | null
}

export default function Settings() {
  const navigate = useNavigate()
  const [settings, setSettings] = useState<Setting[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editingSettings, setEditingSettings] = useState<Record<string, any>>({})
  const [newSetting, setNewSetting] = useState({
    key: '',
    value: '',
    description: ''
  })

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const settingsData = await cmsService.getSettings()
      setSettings(settingsData)
      
      // Initialize editing state
      const editingState: Record<string, any> = {}
      settingsData.forEach(setting => {
        editingState[setting.key] = typeof setting.value === 'string' 
          ? setting.value.replace(/^"|"$/g, '') // Remove quotes from string values
          : JSON.stringify(setting.value)
      })
      setEditingSettings(editingState)
    } catch (error) {
      console.error('Error loading settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSettingChange = (key: string, value: string) => {
    setEditingSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const saveSetting = async (key: string) => {
    try {
      setSaving(true)
      const rawValue = editingSettings[key]
      
      // Try to parse as JSON, fallback to string
      let parsedValue: any
      try {
        parsedValue = JSON.parse(rawValue)
      } catch {
        parsedValue = rawValue
      }

      const setting = settings.find(s => s.key === key)
      await cmsService.setSetting(key, parsedValue, setting?.description || null)
      
      await loadSettings()
    } catch (error) {
      console.error('Error saving setting:', error)
      alert('Error saving setting. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const deleteSetting = async (key: string) => {
    if (confirm(`Are you sure you want to delete the setting "${key}"?`)) {
      try {
        await cmsService.deleteSetting(key)
        await loadSettings()
      } catch (error) {
        console.error('Error deleting setting:', error)
        alert('Error deleting setting. Please try again.')
      }
    }
  }

  const addNewSetting = async () => {
    if (!newSetting.key.trim()) {
      alert('Setting key is required')
      return
    }

    try {
      setSaving(true)
      
      // Try to parse value as JSON, fallback to string
      let parsedValue: any
      try {
        parsedValue = JSON.parse(newSetting.value)
      } catch {
        parsedValue = newSetting.value
      }

      await cmsService.setSetting(
        newSetting.key.trim(),
        parsedValue,
        newSetting.description.trim() || null
      )
      
      setNewSetting({ key: '', value: '', description: '' })
      await loadSettings()
    } catch (error) {
      console.error('Error adding setting:', error)
      alert('Error adding setting. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex items-center space-x-4 mb-8">
        <Button variant="outline" onClick={() => navigate('/admin')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Site Settings</h1>
          <p className="text-gray-600 mt-2">Manage your website configuration</p>
        </div>
      </div>

      {/* Add New Setting */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Add New Setting</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="space-y-2">
              <Label htmlFor="new-key">Key</Label>
              <Input
                id="new-key"
                value={newSetting.key}
                onChange={(e) => setNewSetting(prev => ({ ...prev, key: e.target.value }))}
                placeholder="setting_key"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-value">Value (JSON or string)</Label>
              <Input
                id="new-value"
                value={newSetting.value}
                onChange={(e) => setNewSetting(prev => ({ ...prev, value: e.target.value }))}
                placeholder='"string value" or {"key": "value"}'
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-description">Description</Label>
              <Input
                id="new-description"
                value={newSetting.description}
                onChange={(e) => setNewSetting(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Setting description"
              />
            </div>
          </div>
          <Button onClick={addNewSetting} disabled={saving}>
            <Plus className="h-4 w-4 mr-2" />
            Add Setting
          </Button>
        </CardContent>
      </Card>

      {/* Existing Settings */}
      <div className="space-y-4">
        {settings.map((setting) => (
          <Card key={setting.key}>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 items-start">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Key</Label>
                  <p className="font-mono text-sm bg-gray-100 p-2 rounded mt-1">{setting.key}</p>
                  {setting.description && (
                    <p className="text-sm text-gray-600 mt-2">{setting.description}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`value-${setting.key}`}>Value</Label>
                  <Textarea
                    id={`value-${setting.key}`}
                    value={editingSettings[setting.key] || ''}
                    onChange={(e) => handleSettingChange(setting.key, e.target.value)}
                    rows={3}
                    className="font-mono text-sm"
                  />
                </div>
                
                <div className="flex flex-col space-y-2">
                  <Button
                    onClick={() => saveSetting(setting.key)}
                    disabled={saving}
                    size="sm"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => deleteSetting(setting.key)}
                    size="sm"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {settings.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-600">No settings found. Add your first setting above.</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
