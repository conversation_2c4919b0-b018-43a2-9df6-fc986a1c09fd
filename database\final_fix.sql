-- Final Fix Script - No UUID casting issues
-- This script uses individual INSERT statements to avoid UUID comparison problems

-- Step 1: Check current state
SELECT 'Current database state:' as info;
SELECT 'Products:' as table_name, count(*) as count FROM products
UNION ALL
SELECT 'Brands:' as table_name, count(*) as count FROM brands
UNION ALL
SELECT 'Categories:' as table_name, count(*) as count FROM categories;

-- Step 2: Fix products without valid brands
DO $$
DECLARE
    default_brand_id UUID;
    brand_count INTEGER;
BEGIN
    SELECT count(*) INTO brand_count FROM brands;
    
    IF brand_count = 0 THEN
        INSERT INTO brands (name, description, featured)
        VALUES ('Default Brand', 'Default brand for products', false)
        RETURNING id INTO default_brand_id;
        RAISE NOTICE 'Created default brand with ID: %', default_brand_id;
    ELSE
        SELECT id INTO default_brand_id FROM brands LIMIT 1;
        RAISE NOTICE 'Using existing brand with ID: %', default_brand_id;
    END IF;
    
    UPDATE products 
    SET brand_id = default_brand_id 
    WHERE brand_id IS NULL 
       OR brand_id NOT IN (SELECT id FROM brands);
END $$;

-- Step 3: Add category_id column if needed
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category_id') THEN
        ALTER TABLE products ADD COLUMN category_id UUID;
        RAISE NOTICE 'Added category_id column to products table';
    END IF;
END $$;

-- Step 4: Create main categories (using DO blocks to handle conflicts safely)
DO $$
BEGIN
    INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active)
    VALUES ('550e8400-e29b-41d4-a716-************', 'Air Handling Units', 'air-handling-units', 'Complete air handling systems', NULL, 1, true);
EXCEPTION WHEN unique_violation THEN
    -- Category already exists, update it
    UPDATE categories SET
        name = 'Air Handling Units',
        description = 'Complete air handling systems',
        sort_order = 1
    WHERE id = '550e8400-e29b-41d4-a716-************' OR slug = 'air-handling-units';
END $$;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-************', 'Heat Recovery Systems', 'heat-recovery-systems', 'Energy efficient heat recovery', NULL, 2, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-446655440003', 'Fan Coil Units', 'fan-coil-units', 'Compact heating and cooling', NULL, 3, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-446655440004', 'Cooling Systems', 'cooling-systems', 'Precision cooling systems', NULL, 4, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-446655440005', 'Condensing Units', 'condensing-units', 'Outdoor condensing units', NULL, 5, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-446655440006', 'Heat Pumps', 'heat-pumps', 'Heat pump systems', NULL, 6, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-446655440007', 'Exhaust Systems', 'exhaust-systems', 'Ventilation and exhaust', NULL, 7, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('550e8400-e29b-41d4-a716-446655440008', 'Electrical Enclosures', 'electrical-enclosures', 'Electrical protection', NULL, 8, true)
ON CONFLICT (id) DO NOTHING;

-- Step 5: Create subcategories
-- Air Handling Unit subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440011', 'AHU General Features', 'ahu-general-features', 'Standard air handling units', '550e8400-e29b-41d4-a716-************', 1, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440012', 'Fresh Air Handling Unit', 'fresh-air-handling-unit', 'Dedicated outdoor air systems', '550e8400-e29b-41d4-a716-************', 2, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440013', 'Hygienic Air Handling Unit', 'hygienic-air-handling-unit', 'Clean room air handlers', '550e8400-e29b-41d4-a716-************', 3, true)
ON CONFLICT (id) DO NOTHING;

-- Heat Recovery subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440021', 'Heat Recovery Ventilation', 'heat-recovery-ventilation', 'HRV systems', '550e8400-e29b-41d4-a716-************', 1, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440022', 'Energy Recovery Ventilation', 'energy-recovery-ventilation', 'ERV systems', '550e8400-e29b-41d4-a716-************', 2, true)
ON CONFLICT (id) DO NOTHING;

-- Fan Coil Unit subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440031', 'Wall Mounted FCU', 'wall-mounted-fcu', 'Wall mounted units', '550e8400-e29b-41d4-a716-446655440003', 1, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440032', 'Ceiling Mounted FCU', 'ceiling-mounted-fcu', 'Ceiling mounted units', '550e8400-e29b-41d4-a716-446655440003', 2, true)
ON CONFLICT (id) DO NOTHING;

-- Cooling System subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440041', 'Precision Air Conditioning', 'precision-air-conditioning', 'High-precision cooling', '550e8400-e29b-41d4-a716-446655440004', 1, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440042', 'Ecology Units', 'ecology-units', 'Environmental control systems', '550e8400-e29b-41d4-a716-446655440004', 2, true)
ON CONFLICT (id) DO NOTHING;

-- Heat Pump subcategories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440051', 'Water Source Heat Pumps', 'water-source-heat-pumps', 'Water source heat pumps', '550e8400-e29b-41d4-a716-446655440006', 1, true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO categories (id, name, slug, description, parent_id, sort_order, is_active) 
VALUES ('650e8400-e29b-41d4-a716-446655440052', 'Air Source Heat Pumps', 'air-source-heat-pumps', 'Air source heat pumps', '550e8400-e29b-41d4-a716-446655440006', 2, true)
ON CONFLICT (id) DO NOTHING;

-- Step 6: Update products to use category_id
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440011' WHERE category = 'AHU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440021' WHERE category = 'HRV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440022' WHERE category = 'ERV' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440031' WHERE category = 'FCU' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440042' WHERE category = 'Ecology Unit' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-446655440005' WHERE category = 'Condensing Unit' AND category_id IS NULL;
UPDATE products SET category_id = '650e8400-e29b-41d4-a716-446655440051' WHERE category = 'Water Source Heat Pump' AND category_id IS NULL;
UPDATE products SET category_id = '550e8400-e29b-41d4-a716-446655440007' WHERE category = 'Exhaust Unit' AND category_id IS NULL;

-- Step 7: Add foreign key constraint if needed
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'products_category_id_fkey' 
        AND table_name = 'products'
    ) THEN
        ALTER TABLE products ADD CONSTRAINT products_category_id_fkey 
        FOREIGN KEY (category_id) REFERENCES categories(id);
        RAISE NOTICE 'Added foreign key constraint for category_id';
    END IF;
END $$;

-- Step 8: Create indexes
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Step 9: Final verification and results
SELECT 'Setup completed successfully!' as message;

SELECT 'Final Results:' as info;
SELECT 'Categories total:' as metric, count(*) as count FROM categories
UNION ALL
SELECT 'Main categories:' as metric, count(*) as count FROM categories WHERE parent_id IS NULL
UNION ALL
SELECT 'Subcategories:' as metric, count(*) as count FROM categories WHERE parent_id IS NOT NULL
UNION ALL
SELECT 'Products with category_id:' as metric, count(*) as count FROM products WHERE category_id IS NOT NULL
UNION ALL
SELECT 'Products with valid brands:' as metric, count(*) as count FROM products p JOIN brands b ON p.brand_id = b.id;

-- Show category hierarchy
SELECT 'Category Hierarchy:' as info;
SELECT 
    CASE WHEN parent_id IS NULL THEN '📁 ' || name ELSE '  └─ 📄 ' || name END as category_tree,
    slug,
    (SELECT count(*) FROM products WHERE category_id = categories.id) as products
FROM categories 
ORDER BY COALESCE(parent_id, id), sort_order;

-- Show sample products
SELECT 'Sample Products:' as info;
SELECT p.name as product, c.name as category, b.name as brand
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
LIMIT 5;
