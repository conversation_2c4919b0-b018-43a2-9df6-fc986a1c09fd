@tailwind base;
@tailwind components;
@tailwind utilities;

/* Nile Pro Design System - Colors inspired by the logo */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 225 15% 20%;

    --card: 0 0% 100%;
    --card-foreground: 225 15% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 225 15% 20%;

    /* Primary blue from logo - main brand color */
    --primary: 200 100% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 200 100% 65%;
    --primary-dark: 200 100% 35%;

    /* Secondary colors */
    --secondary: 210 25% 95%;
    --secondary-foreground: 225 15% 20%;

    --muted: 210 25% 96%;
    --muted-foreground: 215 10% 55%;

    /* Accent orange from logo */
    --accent: 35 100% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 214 20% 88%;
    --input: 214 20% 88%;
    --ring: 200 100% 45%;

    --radius: 0.75rem;

    /* Custom gradients */
    --gradient-primary: linear-gradient(135deg, hsl(200 100% 65%), hsl(200 100% 35%));
    --gradient-hero: linear-gradient(135deg, hsl(225 25% 15%), hsl(200 100% 25%));
    --gradient-accent: linear-gradient(45deg, hsl(35 100% 60%), hsl(15 100% 55%));

    /* Shadows */
    --shadow-primary: 0 10px 40px -10px hsl(200 100% 45% / 0.3);
    --shadow-large: 0 25px 50px -12px hsl(225 25% 15% / 0.25);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 225 25% 8%;
    --foreground: 210 40% 98%;

    --card: 225 25% 10%;
    --card-foreground: 210 40% 98%;

    --popover: 225 25% 10%;
    --popover-foreground: 210 40% 98%;

    --primary: 200 100% 65%;
    --primary-foreground: 225 25% 8%;
    --primary-light: 200 100% 75%;
    --primary-dark: 200 100% 45%;

    --secondary: 225 25% 15%;
    --secondary-foreground: 210 40% 98%;

    --muted: 225 25% 15%;
    --muted-foreground: 215 20% 65%;

    --accent: 35 100% 65%;
    --accent-foreground: 225 25% 8%;

    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 98%;

    --border: 225 25% 20%;
    --input: 225 25% 20%;
    --ring: 200 100% 65%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(200 100% 55%), hsl(200 100% 35%));
    --gradient-hero: linear-gradient(135deg, hsl(225 25% 8%), hsl(200 100% 15%));
    --shadow-primary: 0 10px 40px -10px hsl(200 100% 35% / 0.4);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile-first responsive optimizations */
@layer utilities {

  /* Ensure minimum touch target size for mobile */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Prevent horizontal scrolling on mobile */
  .prevent-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Better text readability on mobile */
  .mobile-text-base {
    @apply text-base leading-relaxed;
  }

  /* Improved spacing for mobile */
  .mobile-spacing {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Mobile-friendly card hover effects */
  @media (hover: hover) {
    .hover-scale:hover {
      transform: scale(1.02);
    }
  }

  /* Disable hover effects on touch devices */
  @media (hover: none) {
    .hover-scale:hover {
      transform: none;
    }
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Animation classes to replace framer-motion */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animation-delay-100 {
    animation-delay: 0.1s;
  }

  .animation-delay-200 {
    animation-delay: 0.2s;
  }

  .animation-delay-300 {
    animation-delay: 0.3s;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.2s ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }
}