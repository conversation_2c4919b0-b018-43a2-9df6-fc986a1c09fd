import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination'
import { cmsService } from '@/services/cmsService'
import { useAuth } from '@/contexts/AuthContext'
import { Package, Building2, FileText, Settings, Plus, Edit, Trash2, LogOut, User, Search, Filter, MoreVertical, Eye, Calendar, TrendingUp } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface DashboardStats {
  totalBrands: number
  totalProducts: number
  totalPages: number
}

export default function AdminDashboard() {
  const { user, signOut } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalBrands: 0,
    totalProducts: 0,
    totalPages: 0
  })
  const [brands, setBrands] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [pages, setPages] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // Search and filter states
  const [brandSearch, setBrandSearch] = useState('')
  const [productSearch, setProductSearch] = useState('')
  const [brandFilter, setBrandFilter] = useState('all')
  const [productFilter, setProductFilter] = useState('all')
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])

  // Pagination states
  const [productsPage, setProductsPage] = useState(1)
  const [productsPerPage] = useState(20)
  const [totalProducts, setTotalProducts] = useState(0)

  const navigate = useNavigate()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async (page: number = productsPage) => {
    try {
      setLoading(true)

      // Load all data
      const [brandsData, productsData, pagesData] = await Promise.all([
        cmsService.getBrands(),
        cmsService.getProducts({
          limit: productsPerPage,
          offset: (page - 1) * productsPerPage
        }),
        cmsService.getPages()
      ])

      setBrands(brandsData)
      setProducts(productsData.data)
      setPages(pagesData)
      setTotalProducts(productsData.count)

      // Calculate stats
      setStats({
        totalBrands: brandsData.length,
        totalProducts: productsData.count,
        totalPages: pagesData.length
      })
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteBrand = async (id: string) => {
    if (confirm('Are you sure you want to delete this brand? This will also delete all associated products.')) {
      try {
        await cmsService.deleteBrand(id)
        loadDashboardData()
      } catch (error) {
        console.error('Error deleting brand:', error)
        alert('Error deleting brand. Please try again.')
      }
    }
  }

  const handleDeleteProduct = async (id: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      try {
        await cmsService.deleteProduct(id)
        loadDashboardData()
      } catch (error) {
        console.error('Error deleting product:', error)
        alert('Error deleting product. Please try again.')
      }
    }
  }

  const handleDeletePage = async (id: string) => {
    if (confirm('Are you sure you want to delete this page?')) {
      try {
        await cmsService.deletePage(id)
        loadDashboardData()
      } catch (error) {
        console.error('Error deleting page:', error)
        alert('Error deleting page. Please try again.')
      }
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      navigate('/admin/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // Filtering functions
  const filteredBrands = brands.filter(brand => {
    const matchesSearch = brand.name.toLowerCase().includes(brandSearch.toLowerCase())
    const matchesFilter = brandFilter === 'all' ||
      (brandFilter === 'featured' && brand.featured) ||
      (brandFilter === 'not-featured' && !brand.featured)
    return matchesSearch && matchesFilter
  })

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(productSearch.toLowerCase())
    const matchesFilter = productFilter === 'all' || product.category === productFilter
    return matchesSearch && matchesFilter
  })

  // Bulk operations
  const handleBulkDeleteBrands = async () => {
    if (selectedBrands.length === 0) return
    if (confirm(`Are you sure you want to delete ${selectedBrands.length} brands? This will also delete all associated products.`)) {
      try {
        await Promise.all(selectedBrands.map(id => cmsService.deleteBrand(id)))
        setSelectedBrands([])
        loadDashboardData()
      } catch (error) {
        console.error('Error deleting brands:', error)
        alert('Error deleting brands. Please try again.')
      }
    }
  }

  const handleBulkDeleteProducts = async () => {
    if (selectedProducts.length === 0) return
    if (confirm(`Are you sure you want to delete ${selectedProducts.length} products?`)) {
      try {
        await Promise.all(selectedProducts.map(id => cmsService.deleteProduct(id)))
        setSelectedProducts([])
        loadDashboardData()
      } catch (error) {
        console.error('Error deleting products:', error)
        alert('Error deleting products. Please try again.')
      }
    }
  }

  // Get unique categories for filtering
  const categories = [...new Set(products.map(p => p.category))]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Dashboard</h2>
          <p className="text-gray-500">Please wait while we fetch your data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">CMS Dashboard</h1>
              <p className="text-gray-600 text-lg">Manage your website content with ease</p>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              {/* User Info Card */}
              <div className="bg-white rounded-lg px-4 py-3 shadow-sm border flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">Welcome back!</div>
                  <div className="text-xs text-gray-500">{user?.email}</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/')}
                  className="bg-white hover:bg-gray-50 border-gray-200"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Website
                </Button>
                <Button
                  variant="outline"
                  onClick={handleSignOut}
                  className="bg-white hover:bg-red-50 border-gray-200 text-red-600 hover:text-red-700"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-500 to-blue-600 border-0 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-100">Total Brands</CardTitle>
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <Building2 className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">{stats.totalBrands}</div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-200 rounded-full"></div>
                <p className="text-xs text-blue-100">
                  {brands.filter(b => b.featured).length} featured brands
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-emerald-500 to-emerald-600 border-0 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-emerald-100">Total Products</CardTitle>
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <Package className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">{stats.totalProducts}</div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-emerald-200 rounded-full"></div>
                <p className="text-xs text-emerald-100">
                  Across {new Set(products.map(p => p.category)).size} categories
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500 to-purple-600 border-0 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-100">Total Pages</CardTitle>
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white mb-2">{stats.totalPages}</div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-200 rounded-full"></div>
                <p className="text-xs text-purple-100">
                  Published content
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Analytics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Featured Brands</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{brands.filter(b => b.featured).length}</div>
              <p className="text-xs text-muted-foreground">
                {((brands.filter(b => b.featured).length / brands.length) * 100 || 0).toFixed(1)}% of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.length}</div>
              <p className="text-xs text-muted-foreground">
                Product categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {products.filter(p => {
                  const created = new Date(p.created_at);
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  return created > weekAgo;
                }).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Products added this week
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Content Management Tabs */}
        <Tabs defaultValue="brands" className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border p-2">
            <TabsList className="grid w-full grid-cols-5 bg-gray-50 rounded-lg p-1">
              <TabsTrigger
                value="brands"
                className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
              >
                <Building2 className="h-4 w-4" />
                <span className="hidden sm:inline">Brands</span>
              </TabsTrigger>
              <TabsTrigger
                value="products"
                className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
              >
                <Package className="h-4 w-4" />
                <span className="hidden sm:inline">Products</span>
              </TabsTrigger>
              <TabsTrigger
                value="categories"
                className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7l-7 7-7-7m14 18l-7-7-7 7" />
                </svg>
                <span className="hidden sm:inline">Categories</span>
              </TabsTrigger>
              <TabsTrigger
                value="pages"
                className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
              >
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Pages</span>
              </TabsTrigger>
              <TabsTrigger
                value="settings"
                className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Settings</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="brands" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-xl font-semibold">Brands Management</h2>
                <p className="text-sm text-gray-600 mt-1">
                  {filteredBrands.length} of {brands.length} brands
                  {selectedBrands.length > 0 && ` • ${selectedBrands.length} selected`}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {selectedBrands.length > 0 && (
                  <Button variant="destructive" size="sm" onClick={handleBulkDeleteBrands}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Selected ({selectedBrands.length})
                  </Button>
                )}
                <Button onClick={() => navigate('/admin/brands/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Brand
                </Button>
              </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search brands..."
                    value={brandSearch}
                    onChange={(e) => setBrandSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={brandFilter} onValueChange={setBrandFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter brands" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Brands</SelectItem>
                  <SelectItem value="featured">Featured Only</SelectItem>
                  <SelectItem value="not-featured">Not Featured</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-4">
              {filteredBrands.length === 0 ? (
                <Card className="p-8 text-center">
                  <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">
                    {brands.length === 0 ? 'No Brands Yet' : 'No Brands Found'}
                  </h3>
                  <p className="text-gray-500 mb-4">
                    {brands.length === 0
                      ? 'Start by adding your first brand to showcase your products.'
                      : 'Try adjusting your search or filter criteria.'
                    }
                  </p>
                  {brands.length === 0 && (
                    <Button onClick={() => navigate('/admin/brands/new')}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Brand
                    </Button>
                  )}
                </Card>
              ) : (
                filteredBrands.map((brand) => (
                  <Card key={brand.id}>
                    <CardContent className="flex items-center justify-between p-6">
                      <div className="flex items-center space-x-4">
                        <Checkbox
                          checked={selectedBrands.includes(brand.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedBrands([...selectedBrands, brand.id])
                            } else {
                              setSelectedBrands(selectedBrands.filter(id => id !== brand.id))
                            }
                          }}
                        />
                        {brand.logo ? (
                          <img src={brand.logo} alt={brand.name} className="h-12 w-12 object-contain rounded-lg border border-gray-200" />
                        ) : (
                          <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <Building2 className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-gray-900">{brand.name}</h3>
                            {brand.featured && (
                              <Badge variant="secondary" className="text-xs">Featured</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">
                            {brand.country && brand.established
                              ? `${brand.country} • Est. ${brand.established}`
                              : brand.country || `Est. ${brand.established}` || 'No details'
                            }
                          </p>
                          {brand.website && (
                            <a
                              href={brand.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs text-blue-600 hover:text-blue-800"
                            >
                              Visit Website
                            </a>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => navigate(`/admin/brands/${brand.id}`)}>
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDeleteBrand(brand.id)} className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="products" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-xl font-semibold">Products Management</h2>
                <p className="text-sm text-gray-600 mt-1">
                  {filteredProducts.length} of {products.length} products
                  {selectedProducts.length > 0 && ` • ${selectedProducts.length} selected`}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {selectedProducts.length > 0 && (
                  <Button variant="destructive" size="sm" onClick={handleBulkDeleteProducts}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Selected ({selectedProducts.length})
                  </Button>
                )}
                <Button onClick={() => navigate('/admin/products/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </Button>
              </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search products..."
                    value={productSearch}
                    onChange={(e) => setProductSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={productFilter} onValueChange={setProductFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-4">
              {filteredProducts.length === 0 ? (
                <Card className="p-8 text-center">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">
                    {products.length === 0 ? 'No Products Yet' : 'No Products Found'}
                  </h3>
                  <p className="text-gray-500 mb-4">
                    {products.length === 0
                      ? 'Add products to your catalog to showcase your offerings.'
                      : 'Try adjusting your search or filter criteria.'
                    }
                  </p>
                  {products.length === 0 && (
                    <Button onClick={() => navigate('/admin/products/new')}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Product
                    </Button>
                  )}
                </Card>
              ) : (
                filteredProducts.map((product) => (
                  <Card key={product.id}>
                    <CardContent className="flex items-center justify-between p-6">
                      <div className="flex items-center space-x-4">
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedProducts([...selectedProducts, product.id])
                            } else {
                              setSelectedProducts(selectedProducts.filter(id => id !== product.id))
                            }
                          }}
                        />
                        {product.images?.[0] ? (
                          <img src={product.images[0]} alt={product.name} className="h-12 w-12 object-cover rounded-lg border border-gray-200" />
                        ) : (
                          <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <Package className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-gray-900">{product.name}</h3>
                            <Badge variant="outline" className="text-xs">{product.category}</Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            Model: {product.model || 'N/A'}
                          </p>
                          <p className="text-xs text-gray-500 truncate max-w-md">
                            {product.description || 'No description available'}
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => navigate(`/admin/products/${product.id}`)}>
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDeleteProduct(product.id)} className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>

            {/* Products Pagination */}
            {Math.ceil(totalProducts / productsPerPage) > 1 && (
              <div className="mt-6 flex flex-col items-center space-y-4">
                <div className="text-sm text-gray-600">
                  Showing {((productsPage - 1) * productsPerPage) + 1} to {Math.min(productsPage * productsPerPage, totalProducts)} of {totalProducts} products
                </div>
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => {
                          if (productsPage > 1) {
                            const newPage = productsPage - 1;
                            setProductsPage(newPage);
                            loadDashboardData(newPage);
                          }
                        }}
                        className={productsPage <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>

                    {/* Page Numbers */}
                    {Array.from({ length: Math.min(5, Math.ceil(totalProducts / productsPerPage)) }, (_, i) => {
                      const totalPages = Math.ceil(totalProducts / productsPerPage);
                      let pageNum: number;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (productsPage <= 3) {
                        pageNum = i + 1;
                      } else if (productsPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = productsPage - 2 + i;
                      }

                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => {
                              setProductsPage(pageNum);
                              loadDashboardData(pageNum);
                            }}
                            isActive={productsPage === pageNum}
                            className="cursor-pointer"
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => {
                          const totalPages = Math.ceil(totalProducts / productsPerPage);
                          if (productsPage < totalPages) {
                            const newPage = productsPage + 1;
                            setProductsPage(newPage);
                            loadDashboardData(newPage);
                          }
                        }}
                        className={productsPage >= Math.ceil(totalProducts / productsPerPage) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Categories Management</h2>
              <Button onClick={() => navigate('/admin/categories')}>
                <Plus className="h-4 w-4 mr-2" />
                Manage Categories
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Product Categories</CardTitle>
                <CardDescription>
                  Manage product categories to organize your inventory
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Categories help organize products and improve navigation for customers. You can create, edit, and delete categories as needed.
                </p>
                <Button onClick={() => navigate('/admin/categories')} className="w-full">
                  Open Category Manager
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pages" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Pages Management</h2>
              <Button onClick={() => navigate('/admin/pages/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Add Page
              </Button>
            </div>

            <div className="grid gap-4">
              {pages.map((page) => (
                <Card key={page.id}>
                  <CardContent className="flex items-center justify-between p-6">
                    <div>
                      <h3 className="font-semibold">{page.title}</h3>
                      <p className="text-sm text-gray-600">/{page.slug}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={page.published ? "default" : "secondary"}>
                        {page.published ? "Published" : "Draft"}
                      </Badge>
                      <Button variant="outline" size="sm" onClick={() => navigate(`/admin/pages/${page.id}`)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDeletePage(page.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Site Settings</h2>
              <Button onClick={() => navigate('/admin/settings')}>
                <Settings className="h-4 w-4 mr-2" />
                Manage Settings
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Quick Settings</CardTitle>
                <CardDescription>Manage your site configuration</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Click "Manage Settings" to configure site-wide options like contact information, SEO settings, and feature toggles.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
